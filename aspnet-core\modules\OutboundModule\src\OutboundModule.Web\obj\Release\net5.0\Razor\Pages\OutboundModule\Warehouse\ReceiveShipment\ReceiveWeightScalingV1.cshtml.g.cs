#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0e4722aa7b495a9c1f3c60982b73a31c82a5354f"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Pages_OutboundModule_Warehouse_ReceiveShipment_ReceiveWeightScalingV1), @"mvc.1.0.razor-page", @"/Pages/OutboundModule/Warehouse/ReceiveShipment/ReceiveWeightScalingV1.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 3 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Layout;

#line default
#line hidden
#nullable disable
#nullable restore
#line 4 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
using Microsoft.AspNetCore.Mvc.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 5 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
using OutboundModule.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 6 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
using OutboundModule.Web.Menus;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemMetadataAttribute("RouteTemplate", "/OutboundModule/Warehouse/ReceiveShipment/ReceiveWeightScalingV1/{VehicleRegId?}")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"0e4722aa7b495a9c1f3c60982b73a31c82a5354f", @"/Pages/OutboundModule/Warehouse/ReceiveShipment/ReceiveWeightScalingV1.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"229e689a848d94625f3fd15edcb2d99cbdb5996f", @"/Pages/_ViewImports.cshtml")]
    public class Pages_OutboundModule_Warehouse_ReceiveShipment_ReceiveWeightScalingV1 : global::Microsoft.AspNetCore.Mvc.RazorPages.Page
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/Pages/OutboundModule/Warehouse/ReceiveShipment/ReceiveWeightScalingV1.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery.inputmask.bundle.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery.datetimepicker.full.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery-migrate-3.0.0.min.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery-ui.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/moment.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("form-group"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("goodClasses"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("align", new global::Microsoft.AspNetCore.Html.HtmlString("center"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("bd-bot"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("colspan", new global::Microsoft.AspNetCore.Html.HtmlString("2"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding-top:7px;font-size:10px"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("txtInfo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("text-align:center;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding-top:10px; text-align:right; padding-right:20px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardBodyTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 9 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
  
    PageLayout.Content.Title = "Weight Scaling";
    PageLayout.Content.BreadCrumb.Add("Outbound");
    PageLayout.Content.BreadCrumb.Add("Warehouse");
    PageLayout.Content.MenuItemName = OutboundModuleMenus.ObReceiveShipment;
    ViewBag.Title = "Weight Scaling";
    ViewBag.MetaDescription = "";

#line default
#line hidden
#nullable disable
            WriteLiteral("\r\n");
            DefineSection("scripts", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f11354", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f12595", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f13830", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f15065", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f16306", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f17547", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_5.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            WriteLiteral(@"<script src=""/js/JsBarcode.code39.min.js""></script>
<script src=""https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"">
</script>
<link href=""/css/custom.css"" rel=""Stylesheet"" type=""text/css"" />
<link href=""/css/jquery.datetimepicker.min.css"" rel=""Stylesheet"" type=""text/css"" />
<link href=""/css/scale.css"" rel=""Stylesheet"" type=""text/css"" />
<style>
    #dtReceived th,
    td {
        white-space: nowrap;
    }
</style>
");
            WriteLiteral("<input type=\"hidden\" id=\"VehicleRegId\"");
            BeginWriteAttribute("value", " value=\"", 1748, "\"", 1775, 1);
#nullable restore
#line 40 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
WriteAttributeValue("", 1756, Model.VehicleRegId, 1756, 19, false);

#line default
#line hidden
#nullable disable
            EndWriteAttribute();
            WriteLiteral(">\r\n<input type=\"hidden\" id=\"UrlQRCode\"");
            BeginWriteAttribute("value", " value=\"", 1814, "\"", 1838, 1);
#nullable restore
#line 41 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\ReceiveShipment\ReceiveWeightScalingV1.cshtml"
WriteAttributeValue("", 1822, Model.UrlQRCode, 1822, 16, false);

#line default
#line hidden
#nullable disable
            EndWriteAttribute();
            WriteLiteral(">\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f20403", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card-body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f20670", async() => {
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f20954", async() => {
                        WriteLiteral(@"
            <div class=""col-4"">
                <div class=""row"">
                    <div class=""col-12 d-flex"">
                        <strong class=""mt-2"" style=""margin-right: 10px;"">Scale by</strong><input type=""text"" id=""txtVehicleRegNo"" readonly style=""font-size: 15px; font-weight: bold; text-transform: uppercase; width:60% !important "" class=""form-control"">
                        <button id=""btnSelect"" class=""btn-primary btn"">SELECT</button>
                    </div>
                </div>
            </div>
            <div class=""col-3"">
                <div class=""row"">
                    <div class=""col-12 d-flex""><strong class=""mt-2"">Total Pieces (NPR/NPX)  </strong> <h2 class=""text-success""><span id=""NPR""></span>/<span id=""NPX""></span></h2></div>
                </div>
            </div>
            <div class=""col-4"">
                <div class=""row"">
                    <div class=""col-12 d-flex"">
                        <strong class=""mt-2"">Total GW (GWR/GWX)  </strong> <");
                        WriteLiteral(@"h2 class=""text-success""><span id=""GWR""></span>/<span id=""GWX""></span></h2>
                    </div>
                </div>
            </div>
            <div class=""col-1 text-right"">  <i id=""btnSettings"" class=""fas fa-cog text-primary"" style=""font-size:36px; cursor:pointer""></i></div>
        ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardBodyTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n\r\n");
            }
            );
            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardTagHelper>();
            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f25069", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card-body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f25336", async() => {
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f25620", async() => {
                        WriteLiteral(@"
            <div class=""col-6"">
                <div class=""row"">
                    <div class=""col-12"">
                        <strong>Scaling by</strong>
                    </div>
                    <div class=""col-5"">
                        <input type=""radio"" value=""PLT"" id=""pallet"" name=""goodsType"" checked onchange=""ResetPalletID_DOPO()"">
                        <label for=""pallet"" style=""margin-left: 2px;margin-top: 5px;"">Pallet</label>
                        &emsp;  <input type=""radio"" value=""DOPO"" id=""dopo"" name=""goodsType"" onchange=""ResetPalletID_DOPO()"">
                        <label for=""dopo"" style=""margin-left: 2px; margin-top: 5px;"">PO/DO/DNN</label>
                    </div>
                    <div class=""col-7 d-flex"">
                        <div class=""input-group mb-3"">
                            <div class=""input-group-prepend"">
                                <span class=""input-group-text"" id=""basic-addon1""><i class=""fas fa-barcode""></i></span>
                ");
                        WriteLiteral(@"            </div>
                            <input class=""form-control"" id=""txtPalletDO"" />
                        </div>
                    </div>
                </div>
                <div class=""row mt-1"">
                    <div class=""col-5"">
                        <strong><label>GW+Base</label></strong>
                        <strong><label id=""txtStatusScale""></label></strong>
                        <input type=""text"" value=""0"" data-default=""00"" id=""gwBase"" name=""length"" class=""gwBase""");
                        BeginWriteAttribute("readonly", " readonly=\"", 4858, "\"", 4869, 0);
                        EndWriteAttribute();
                        WriteLiteral(@">
                    </div>
                    <div class=""col-7"">
                        <div class=""row"">
                            <div class=""col-4"">
                                <strong><label>Length</label></strong>
                                <br /><input type=""text"" id=""Length"" class=""dimensions"">
                            </div>
                            <div class=""col-4"">
                                <strong><label>Width</label></strong>
                                <br /><input type=""text"" id=""Width"" class=""dimensions"">
                            </div>
                            <div class=""col-4"">
                                <strong><label>Height</label></strong>
                                <br /><input type=""text"" id=""Height"" class=""dimensions"">
                            </div>
                        </div>
                        <div class=""row"">
                            <div class=""col-4"">
                                <strong><label");
                        WriteLiteral(@">Base</label></strong>
                                <br /><input type=""text"" value=""0"" id=""base"" name=""base"" data-default=""0"" class=""dimensions"" onfocus=""this.setSelectionRange(0, this.value.length)"">
                            </div>
                            <div class=""col-4"">
                                <strong><label>GW</label></strong>
                                <br /><input type=""text"" value=""0.0"" id=""gw"" name=""gw"" class=""dimensions"" readonly>
                            </div>
                            <div class=""col-4"">
                                <strong><label>Pieces</label></strong>
                                <br /><input type=""text"" value=""1"" id=""pcs"" name=""pcs"" data-default=""1""
                                             class=""dimensions"" onfocus=""this.setSelectionRange(0, this.value.length)"">
                            </div>
                        </div>
                    </div>
                </div>
                <div class=""row mt-2"">
     ");
                        WriteLiteral("               <div class=\"col-5\">\r\n                        <strong><label>Class</label></strong>\r\n                        <div class=\"row\">\r\n                            <div class=\"col-12\">\r\n                                ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f30183", async() => {
                            WriteLiteral("\r\n                                ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                            </div>\r\n");
                        WriteLiteral(@"                        </div>
                    </div>
                    <div class=""col-7"">
                        <div class=""row"">
                            <div class=""col-6"">
                                <strong><label>Dest</label></strong>
                                <br />
");
                        WriteLiteral(@"                                <input type=""text"" id=""Destination"" class=""form-control"" style=""text-transform:uppercase"">
                            </div>
                            <div class=""col-6"">
                                <strong><label>Forwader</label></strong>
                                <br />
");
                        WriteLiteral(@"                                <input type=""text"" id=""Agent"" class=""form-control"" style=""text-transform:uppercase"">
                            </div>
                        </div>
                    </div>
                </div>
                <div class=""row"">
                    <div class=""col-5"">
                        <strong><label>Air/Sea</label></strong>
                        <div class=""row"">
                            <div class=""col-4"">
                                <input type=""radio"" value=""AIR"" id=""Air"" name=""air-sea"" checked>
                                <label for=""Air"" style=""margin-right:10px"">Air</label>
                            </div>
                            <div class=""col-4"">
                                <input type=""radio"" value=""SEA"" id=""Sea"" name=""air-sea"">
                                <label for=""Sea"" style=""margin-right:10px"">Sea</label>
                            </div>
                        </div>
                    </div>
        ");
                        WriteLiteral(@"            <div class=""col-7"">
                        <strong><label>PIN</label></strong>
                        <div class=""row"">
                            <div class=""col-12"">
                                <input type=""radio"" value=""UN3481"" id=""UN3481"" name=""pin"">
                                <label for=""UN3481"" style=""margin-right:10px"">UN3481</label>
                                <input type=""radio"" value=""UN3091"" id=""UN3091"" name=""pin"">
                                <label for=""UN3091"" style=""margin-right:10px"">UN3091</label>
                                <input type=""radio"" value=""DG"" id=""DG"" name=""pin"">
                                <label for=""DG"" style=""margin-right:10px"">DG</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=""row mt-2"">
                    <div class=""col-12"">
                        <strong><label>Repacking Services</label></strong>
                 ");
                        WriteLiteral(@"       <br />
                        <input type=""checkbox"" id=""Go"" name=""Repacking"" value=""Gỗ (G)"">
                        <label for=""Go"" style=""margin-right:10px"">Gỗ (G)</label>
                        <input type=""checkbox"" id=""Ton"" name=""Repacking"" value=""Tôn (T)"">
                        <label for=""Ton"" style=""margin-right:10px"">Tôn (T)</label>
                        <input type=""checkbox"" id=""LuoiThep"" name=""Repacking"" value=""Lưới thép (L)"">
                        <label for=""LuoiThep"" style=""margin-right:10px"">Lưới thép (L)</label>
                        <input type=""checkbox"" id=""HangRong"" name=""Repacking"" value=""Hàng rỗng"">
                        <label for=""HangRong"" style=""margin-right:10px"">Hàng rỗng</label>
                        <input type=""checkbox"" id=""Repacking"" name=""Repacking"" value=""Repacking khác (K)"">
                        <label for=""Repacking"" style=""margin-right:10px"">Repacking khác (K)</label>
                        <input type=""text"" id=""txtRepacking""");
                        WriteLiteral(@" class=""form-control"" style=""width:150px;display:initial !important;"">
                    </div>

                </div>
                <div class=""row mt-2"">
                    <div class=""col-12"" style=""text-align:right;border-top: solid 1px #ccc; padding-top: 15px;"">
");
                        WriteLiteral("                        <button id=\"btnMix\" class=\"btn-primary btn btn-bottom \">Mix DO</button>\r\n                        <button id=\"btnPrint\" class=\"btn-primary btn btn-bottom \">Save & Print (F2)</button>\r\n");
                        WriteLiteral(@"                        <button id=""btnFinish"" class=""btn-primary btn btn-bottom "">Finish</button>
                    </div>
                </div>
            </div>
            <div class=""col-6"" style=""border-left: 1px solid #ccc;"">
                <div class=""row"">
                    <div class=""col-9"">
                        <strong>Received</strong>
                    </div>
                    <div class=""col-3 text-right"">
                        <a href=""/OutboundModule/Warehouse/SearchDo/SearchDo"" id=""btnSelect"" class=""btn-primary btn"" target=""_blank""><i class=""fa fa-search""></i>Search DO</a>
");
                        WriteLiteral(@"                    </div>
                </div>
                <div class=""row"">
                    <div style=""overflow-x:auto; max-height: 490px; width: 100%;margin-top: 10px;  margin-left: 10px;"">
                        <table class=""table table-striped dataTable table-bordered"" id=""dtReceived"" style=""display:none;"">
                            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("thead", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f37409", async() => {
                            WriteLiteral("\r\n                                ");
                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f37725", async() => {
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f38050", async() => {
                                    WriteLiteral("D");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f39403", async() => {
                                    WriteLiteral("P");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n\r\n");
                                WriteLiteral("                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f40811", async() => {
                                    WriteLiteral("No");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f42165", async() => {
                                    WriteLiteral("DO");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f43519", async() => {
                                    WriteLiteral("Pallet ID");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f44880", async() => {
                                    WriteLiteral("NPR");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f46235", async() => {
                                    WriteLiteral("GWR");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f47590", async() => {
                                    WriteLiteral("Dim");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f48945", async() => {
                                    WriteLiteral("Dest");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f50301", async() => {
                                    WriteLiteral("Pin Remarks");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f51664", async() => {
                                    WriteLiteral("Remarks Air/Sea/Mix");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f53035", async() => {
                                    WriteLiteral("Received Time");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                                ");
                            }
                            );
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                            {
                                await __tagHelperExecutionContext.SetOutputContentAsync();
                            }
                            Write(__tagHelperExecutionContext.Output);
                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                            WriteLiteral("\r\n                            ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                            <tbody>\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral(@"
        <div style=""display:none"" id=""PRINT"">
            <style>
                .bd-bot {
                    border-bottom: 1px solid;
                }
            </style>
            <table width=""431"" style=""border-collapse: collapse;"">
                <tbody>
                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f57652", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f57949", async() => {
                            WriteLiteral("\r\n                            <img src=\"/images/logo/logo-als.png\" height=\"30px\">\r\n                        ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f59280", async() => {
                            WriteLiteral("<span style=\"font-size:14px; font-weight:bold;\">WEIGHT SLIP<br /> ALS Bắc Ninh</span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f61652", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f61949", async() => {
                            WriteLiteral("<span style=\"font-size:13px;\">DO NO: </span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=\"font-size:40px; font-weight:bold;\" id=\"txtPoNumber\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f64466", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f64763", async() => {
                            WriteLiteral(" <span style=\"font-size:13px\">PALLET NO:</span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;</span> <span style=\"font-size:13px;\" id=\"txtPalletNo\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f67248", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f67545", async() => {
                            WriteLiteral("<svg id=\"barcode\"></svg>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f69952", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f70249", async() => {
                            WriteLiteral(@"<span style=""font-size:13px;"">G.W:</span> <span style=""font-size:14px; font-weight:bold;"">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=""font-size:50px; font-weight:bold;"" id=""txtGW""></span> <span style=""font-size:22px;"" id=""txtPcs""></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f72831", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f73128", async() => {
                            WriteLiteral("<span style=\"font-size:13px;\">DIM(Cm):</span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=\"font-size:34px; font-weight:bold;\" id=\"txtDim\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f75631", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f75928", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f78363", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f78660", async() => {
                            WriteLiteral("<svg id=\"barcodeGroup\"></svg>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f80981", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f81278", async() => {
                            WriteLiteral("<span style=\"font-size:13px; font-weight:bold;\" id=\"txtGroupNo\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_10);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_9);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f83735", async() => {
                        WriteLiteral("\r\n");
                        WriteLiteral("                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "0e4722aa7b495a9c1f3c60982b73a31c82a5354f84075", async() => {
                            WriteLiteral("<span style=\"font-size:45px; font-weight:bold;\" id=\"txtDest\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                </tbody>\r\n            </table>\r\n        </div>\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardBodyTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardTagHelper>();
            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IPageLayout PageLayout { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IHtmlLocalizer<OutboundModuleResource> L { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScalingV1Model> Html { get; private set; }
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScalingV1Model> ViewData => (global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScalingV1Model>)PageContext?.ViewData;
        public OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScalingV1Model Model => ViewData.Model;
    }
}
#pragma warning restore 1591
