#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "169af39cd65cd1d7d95f68a96e8131e230dd1cba"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Pages_MasterDataModule_Vehicles_CreateModal), @"mvc.1.0.razor-page", @"/Pages/MasterDataModule/Vehicles/CreateModal.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using Microsoft.AspNetCore.Mvc.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 3 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using MasterDataModule.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 4 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;

#line default
#line hidden
#nullable disable
#nullable restore
#line 5 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using MasterDataModule.Web.Pages.MasterDataModule.Vehicles;

#line default
#line hidden
#nullable disable
#nullable restore
#line 6 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using MasterDataModule.Vehicles;

#line default
#line hidden
#nullable disable
#nullable restore
#line 7 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
using System.Globalization;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"169af39cd65cd1d7d95f68a96e8131e230dd1cba", @"/Pages/MasterDataModule/Vehicles/CreateModal.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"229e689a848d94625f3fd15edcb2d99cbdb5996f", @"/Pages/_ViewImports.cshtml")]
    public class Pages_MasterDataModule_Vehicles_CreateModal : global::Microsoft.AspNetCore.Mvc.RazorPages.Page
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("VehicleRegNo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("onKeyup", new global::Microsoft.AspNetCore.Html.HtmlString("return CheckVehicleRegNo();"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("text", "Save", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("submit"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnSave"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("data-ajaxForm", new global::Microsoft.AspNetCore.Html.HtmlString("true"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("asp-page", "/MasterDataModule/Vehicles/CreateModal", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("autocomplete", new global::Microsoft.AspNetCore.Html.HtmlString("off"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalHeaderTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalBodyTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpSelectTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalFooterTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 10 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
   Layout = null; 

#line default
#line hidden
#nullable disable
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba9055", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba9317", async() => {
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-header", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba9596", async() => {
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalHeaderTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper);
                    BeginWriteTagHelperAttribute();
#nullable restore
#line 14 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                     WriteLiteral(L["NewVehicle"].Value);

#line default
#line hidden
#nullable disable
                    __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper.Title = __tagHelperStringValueBuffer;
                    __tagHelperExecutionContext.AddTagHelperAttribute("title", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper.Title, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba11553", async() => {
                        WriteLiteral("\r\n\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba11854", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 18 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.VehicleRegNo);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        BeginWriteTagHelperAttribute();
#nullable restore
#line 18 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                                                 WriteLiteral(L["VehicleRegNo"].Value);

#line default
#line hidden
#nullable disable
                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.Label = __tagHelperStringValueBuffer;
                        __tagHelperExecutionContext.AddTagHelperAttribute("label", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.Label, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_0);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            <label class=\"red\" style=\"display:none\" id=\"lblCheckExist\"></label>\r\n            <label class=\"red\" style=\"display:none\" id=\"lblCheck\"></label>\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba14909", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 21 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.Code);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        BeginWriteTagHelperAttribute();
#nullable restore
#line 21 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                                         WriteLiteral(L["Company"].Value);

#line default
#line hidden
#nullable disable
                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.Label = __tagHelperStringValueBuffer;
                        __tagHelperExecutionContext.AddTagHelperAttribute("label", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.Label, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba17580", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 22 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.NumberOfDoors);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba19405", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 23 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.VehicleDim);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-select", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba21227", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpSelectTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper);
#nullable restore
#line 24 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.VehicleType);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
#nullable restore
#line 24 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems = Model.VehicleTypeSelectList;

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-items", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        BeginWriteTagHelperAttribute();
#nullable restore
#line 24 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                                                                                         WriteLiteral(L["VehicleType"].Value);

#line default
#line hidden
#nullable disable
                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label = __tagHelperStringValueBuffer;
                        __tagHelperExecutionContext.AddTagHelperAttribute("label", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-select", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba24588", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpSelectTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper);
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.VehicleTypeOnwer);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems = Model.VehicleTypeOwnerList;

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-items", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        BeginWriteTagHelperAttribute();
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                                                                                             WriteLiteral(L["VehicleTypeOwner"].Value);

#line default
#line hidden
#nullable disable
                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label = __tagHelperStringValueBuffer;
                        __tagHelperExecutionContext.AddTagHelperAttribute("label", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-select", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "169af39cd65cd1d7d95f68a96e8131e230dd1cba27966", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpSelectTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper);
#nullable restore
#line 27 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.Vehicle.VehicleLoadWeight);

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
#nullable restore
#line 27 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems = Model.VehicleLoadWeightSelectList;

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("asp-items", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.AspItems, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        BeginWriteTagHelperAttribute();
#nullable restore
#line 27 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
                                                                                                     WriteLiteral(L["VehicleLoadWeight"].Value);

#line default
#line hidden
#nullable disable
                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label = __tagHelperStringValueBuffer;
                        __tagHelperExecutionContext.AddTagHelperAttribute("label", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpSelectTagHelper.Label, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n        ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalBodyTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("   \r\n\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-footer", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba32236", async() => {
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "169af39cd65cd1d7d95f68a96e8131e230dd1cba32535", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_2.Value;
                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
#nullable restore
#line 32 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                        __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n        ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalFooterTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper);
#nullable restore
#line 31 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\MasterDataModule\src\MasterDataModule.Web\Pages\MasterDataModule\Vehicles\CreateModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper.Buttons = (AbpModalButtons.Cancel);

#line default
#line hidden
#nullable disable
                    __tagHelperExecutionContext.AddTagHelperAttribute("buttons", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper.Buttons, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
            __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
            __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
            __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper.Page = (string)__tagHelperAttribute_6.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IHtmlLocalizer<MasterDataModuleResource> L { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<CreateModalModel> Html { get; private set; }
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<CreateModalModel> ViewData => (global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<CreateModalModel>)PageContext?.ViewData;
        public CreateModalModel Model => ViewData.Model;
    }
}
#pragma warning restore 1591
