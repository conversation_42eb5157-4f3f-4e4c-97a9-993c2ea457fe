#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "701f173e88f87cf270f2cab3eef166c678c648ab"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Pages_OutboundModule_Warehouse_SearchDo_DetailPalletModal), @"mvc.1.0.razor-page", @"/Pages/OutboundModule/Warehouse/SearchDo/DetailPalletModal.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Layout;

#line default
#line hidden
#nullable disable
#nullable restore
#line 3 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
using Microsoft.AspNetCore.Mvc.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 4 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
using OutboundModule.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 5 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
using OutboundModule.Web.Menus;

#line default
#line hidden
#nullable disable
#nullable restore
#line 6 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"701f173e88f87cf270f2cab3eef166c678c648ab", @"/Pages/OutboundModule/Warehouse/SearchDo/DetailPalletModal.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"229e689a848d94625f3fd15edcb2d99cbdb5996f", @"/Pages/_ViewImports.cshtml")]
    public class Pages_OutboundModule_Warehouse_SearchDo_DetailPalletModal : global::Microsoft.AspNetCore.Mvc.RazorPages.Page
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/Pages/OutboundModule/Warehouse/SearchDo/detailPalletModal.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("TitleHeader"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("font-weight: bold; font-size: 18px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("DoId"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("GraiObjectGroupIsn"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("GraiObjectIsn"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("PalletNo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("VehicleRegId"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("doNo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("title", "Factory Pickup", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("title", "Transit NBA", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("align", new global::Microsoft.AspNetCore.Html.HtmlString("center"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("bd-bot"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("colspan", new global::Microsoft.AspNetCore.Html.HtmlString("2"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding-top:7px;font-size:10px"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("txtInfo"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("text-align:center;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("style", new global::Microsoft.AspNetCore.Html.HtmlString("padding-top:10px; text-align:right; padding-right:20px;"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("text", "Edit", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("submit"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_20 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnEdit"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_21 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("text", "Print Label", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_22 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnPrint"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_23 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("text", "History", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_24 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnHistory"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_25 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("justify-content-center"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_26 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("autocomplete", new global::Microsoft.AspNetCore.Html.HtmlString("off"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalHeaderTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalBodyTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabsTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalFooterTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 9 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
  
    Layout = null;

#line default
#line hidden
#nullable disable
            WriteLiteral("<style>\r\n    .checkbox_classes {\r\n        padding: 0px 10px;\r\n    }\r\n\r\n    .modal-xl {\r\n        max-width: 1300px !important;\r\n    }\r\n</style>\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab15785", async() => {
            }
            );
            __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
            __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_0.Value;
            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab16962", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-header", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab17230", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalHeaderTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalHeaderTagHelper);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_1);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <div style=\"display:none\">\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab18506", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.DoId);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab20295", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 26 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.GraiObjectGroupIsn);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab22098", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 27 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.GraiObjectIsn);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab23896", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 28 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.PalletNo);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab25689", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 29 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.VehicleRegId);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-input", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "701f173e88f87cf270f2cab3eef166c678c648ab27486", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form.AbpInputTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper);
#nullable restore
#line 30 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor = ModelExpressionProvider.CreateModelExpression(ViewData, __model => __model.DoNo);

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("asp-for", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Form_AbpInputTagHelper.AspFor, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_8);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n        <input type=\"hidden\" id=\"UrlQRCode\"");
                BeginWriteAttribute("value", " value=\"", 1237, "\"", 1261, 1);
#nullable restore
#line 31 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
WriteAttributeValue("", 1245, Model.UrlQRCode, 1245, 16, false);

#line default
#line hidden
#nullable disable
                EndWriteAttribute();
                WriteLiteral(">\r\n    </div>\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab29818", async() => {
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tabs", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab30103", async() => {
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tab", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab30394", async() => {
                            WriteLiteral(@"
                <div class=""row"">
                    <div class=""col-md-6"">
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                PCS
                            </div>
                            <div class=""col-md-3"">
                                <input type=""number"" id=""pcs"" class=""form-control"" readonly />
                            </div>
                            <div class=""col-md-2"">
                            </div>
                            <div class=""col-md-1"">
                                GW
                            </div>
                            <div class=""col-md-3"">
                                <input type=""number"" id=""gw"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
         ");
                            WriteLiteral(@"                       DIM
                            </div>
                            <div class=""col-md-1"">
                                L
                            </div>
                            <div class=""col-md-2"">
                                <input type=""number"" id=""Length"" class=""form-control"" readonly />
                            </div>
                            <div class=""col-md-1"">
                                W
                            </div>
                            <div class=""col-md-2"">
                                <input type=""number"" id=""Width"" class=""form-control"" readonly />
                            </div>
                            <div class=""col-md-1"">
                                H
                            </div>
                            <div class=""col-md-2"">
                                <input type=""number"" id=""Height"" class=""form-control"" readonly />
                            </div>
                        </div>
");
                            WriteLiteral(@"

                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Class
                            </div>
                            <div class=""col-md-9 mt-2"">
                                <div class=""row"" id=""goodClasses"">
                                </div>
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Air/Sea
                            </div>
                            <div class=""col-2"">
                                <input type=""radio"" value=""AIR"" id=""Air"" name=""air-sea"" checked>
                                <label for=""Air"" style=""margin-right:10px"">Air</label>
                            </div>
                            <div class=""col-2"">
                                <input type=""radio"" value=""SEA"" id");
                            WriteLiteral(@"=""Sea"" name=""air-sea"">
                                <label for=""Sea"" style=""margin-right:10px"">Sea</label>
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Pin
                            </div>
                            <div class=""col-9"">
                                <input type=""radio"" value=""UN3481"" id=""UN3481"" name=""pin"">
                                <label for=""UN3481"" style=""margin-right:10px"">UN3481</label>
                                <input type=""radio"" value=""UN3091"" id=""UN3091"" name=""pin"">
                                <label for=""UN3091"" style=""margin-right:10px"">UN3091</label>
                                <input type=""radio"" value=""DG"" id=""DG"" name=""pin"">
                                <label for=""DG"" style=""margin-right:10px"">DG</label>
                            </div>
                   ");
                            WriteLiteral(@"     </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Repacking
                            </div>
                            <div class=""col-9"">
                                <input type=""checkbox"" id=""Go"" name=""Repacking"" value=""Gỗ (G)"">
                                <label for=""Go"" style=""margin-right:10px"">Gỗ (G)</label>
                                <input type=""checkbox"" id=""Ton"" name=""Repacking"" value=""Tôn (T)"">
                                <label for=""Ton"" style=""margin-right:10px"">Tôn (T)</label>
                                <input type=""checkbox"" id=""LuoiThep"" name=""Repacking"" value=""Lưới thép (L)"">
                                <label for=""LuoiThep"" style=""margin-right:10px"">Lưới thép (L)</label>
                                <input type=""checkbox"" id=""HangRong"" name=""Repacking"" value=""Hàng rỗng"">
                                <label for=""HangRong"" s");
                            WriteLiteral(@"tyle=""margin-right:10px"">Hàng rỗng</label>
                                <br>
                                <input type=""checkbox"" id=""Repacking"" name=""Repacking"" value=""Repacking khác (K)"">
                                <label for=""Repacking"" style=""margin-right:10px"">Repacking khác (K)</label>
                                <input type=""text"" id=""txtRepacking"" class=""form-control"" style=""width:220px; float:right;display:initial !important;"">
                            </div>
                        </div>
                    </div>
                    <div class=""col-md-6"">
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Destination
                            </div>
                            <div class=""col-md-9"">
                                <select id=""ddlDestination"" class=""form-control select2"">
                                </select>
                            </");
                            WriteLiteral(@"div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Forwarder
                            </div>
                            <div class=""col-md-9"">
                                <select id=""ddlAgent"" class=""form-control select2"">
                                </select>
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Location
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtLocation"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3");
                            WriteLiteral(@""">
                                GR Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtGRTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck No
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckNo"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Seal No
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtSeal"" class=""form-contr");
                            WriteLiteral(@"ol"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Create Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtCreateTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Transit Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTransitTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            ");
                            WriteLiteral(@"<div class=""col-md-3"">
                                Arrival Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtArrivalTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                    </div>
                </div>
            ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper);
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Title = (string)__tagHelperAttribute_9.Value;
                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_9);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tab", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab42161", async() => {
                            WriteLiteral(@"
                <div class=""row"">
                    <div class=""col-md-6"">
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                DO Number
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtDoNumber"" class=""form-control"" readonly/>
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                MAWB
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtMAWB"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
");
                            WriteLiteral(@"                                HAWB
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtHAWB"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                PCS MAWB
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtPcsMawb"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                GW MAWB
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtGwMawb"" class=""form-control"" rea");
                            WriteLiteral(@"donly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                SLI Print Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtSliPrintTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                SLI Begin Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtSliBeginTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                           ");
                            WriteLiteral(@" <div class=""col-md-3"">
                                SLI Finish Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtSliFinishTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Dest MAWB
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtDestMawb"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                FWD MAWB
                            </div>
                            <div class=""col-md-9"">
                              ");
                            WriteLiteral(@"  <input id=""txtFwdMawb"" class=""form-control"" readonly/>
                            </div>
                        </div>
                    </div>
                    <div class=""col-md-6"">
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                CD
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtCD"" class=""form-control"" />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                CD Type
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtCDType"" class=""form-control"" />
                            </div>
                        </div>
                        <div clas");
                            WriteLiteral(@"s=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                CD Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtCDTime"" class=""form-control"" />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck No
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckNo1"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Seal HQ
                            </div>
                            <div class=""c");
                            WriteLiteral(@"ol-md-9"">
                                <input id=""txtSealHQ"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Seal AN
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtSealAN"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck Create Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckCreateTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                  ");
                            WriteLiteral(@"      <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck Transit Time
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckTransitTime"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck Arrival NBA
                            </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckArrivalNBA"" class=""form-control"" readonly />
                            </div>
                        </div>
                        <div class=""row form-group align-items-center"">
                            <div class=""col-md-3"">
                                Truck Finish NBA
       ");
                            WriteLiteral(@"                     </div>
                            <div class=""col-md-9"">
                                <input id=""txtTruckFinishNBA"" class=""form-control"" readonly />
                            </div>
                        </div>
                    </div>
                </div>
            ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper);
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Title = (string)__tagHelperAttribute_10.Value;
                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_10);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n        ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabsTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral(@"
        <div style=""display:none"" id=""PRINT"">
            <style>
                .bd-bot {
                    border-bottom: 1px solid;
                }
            </style>
            <table width=""431"" style=""border-collapse: collapse;"">
                <tbody>
                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab53820", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab54117", async() => {
                            WriteLiteral("\r\n                            <img src=\"/images/logo/logo-als.png\" height=\"30px\">\r\n                        ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab55448", async() => {
                            WriteLiteral("<span style=\"font-size:14px; font-weight:bold;\">WEIGHT SLIP<br /> ALS Bắc Ninh</span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab57822", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab58119", async() => {
                            WriteLiteral("<span style=\"font-size:13px;\">DO NO: </span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=\"font-size:40px; font-weight:bold;\" id=\"txtPoNumber\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab60637", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab60934", async() => {
                            WriteLiteral(" <span style=\"font-size:13px\">PALLET NO:</span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;</span> <span style=\"font-size:13px;\" id=\"txtPalletNo\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab63420", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab63717", async() => {
                            WriteLiteral("<svg id=\"barcode\"></svg>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab66126", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab66423", async() => {
                            WriteLiteral(@"<span style=""font-size:13px;"">G.W:</span> <span style=""font-size:14px; font-weight:bold;"">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=""font-size:50px; font-weight:bold;"" id=""txtGW""></span> <span style=""font-size:22px;"" id=""txtPcs""></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab69006", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab69303", async() => {
                            WriteLiteral("<span style=\"font-size:13px;\">DIM(Cm):</span><span style=\"font-size:14px; font-weight:bold;\">&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;</span><span style=\"font-size:34px; font-weight:bold;\" id=\"txtDim\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab71807", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab72104", async() => {
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_14);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_15);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab74540", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab74837", async() => {
                            WriteLiteral("<svg id=\"barcodeGroup\"></svg>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab77159", async() => {
                        WriteLiteral("\r\n                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab77456", async() => {
                            WriteLiteral("<span style=\"font-size:13px; font-weight:bold;\" id=\"txtGroupNo\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                    ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab79914", async() => {
                        WriteLiteral("\r\n");
                        WriteLiteral("                        ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("td", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab80254", async() => {
                            WriteLiteral("<span style=\"font-size:45px; font-weight:bold;\" id=\"txtDest\"></span>");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n                    ");
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n                </tbody>\r\n            </table>\r\n        </div>\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalBodyTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalBodyTagHelper);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-modal-footer", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab83395", async() => {
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab83682", async() => {
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_18.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_18);
#nullable restore
#line 430 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                    __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_20);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab85903", async() => {
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_21.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_21);
#nullable restore
#line 431 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                    __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_22);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "701f173e88f87cf270f2cab3eef166c678c648ab88124", async() => {
                    }
                    );
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_23.Value;
                    __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_23);
#nullable restore
#line 432 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                    __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalFooterTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper);
#nullable restore
#line 429 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper.Buttons = AbpModalButtons.Cancel;

#line default
#line hidden
#nullable disable
                __tagHelperExecutionContext.AddTagHelperAttribute("buttons", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalFooterTagHelper.Buttons, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_25);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalTagHelper>();
            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper);
#nullable restore
#line 22 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal.AbpModalSize.ExtraLarge;

#line default
#line hidden
#nullable disable
            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_26);
#nullable restore
#line 22 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\OutboundModule\src\OutboundModule.Web\Pages\OutboundModule\Warehouse\SearchDo\DetailPalletModal.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper.Scrollable = true;

#line default
#line hidden
#nullable disable
            __tagHelperExecutionContext.AddTagHelperAttribute("scrollable", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Modal_AbpModalTagHelper.Scrollable, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IHtmlLocalizer<OutboundModuleResource> L { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.DetailPalletModalModel> Html { get; private set; }
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.DetailPalletModalModel> ViewData => (global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.DetailPalletModalModel>)PageContext?.ViewData;
        public OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.DetailPalletModalModel Model => ViewData.Model;
    }
}
#pragma warning restore 1591
