#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "d2facc9ba60ebd9da913916c14213bdf5152c2c4"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Themes_Lepton_Components_MainMenu_Default), @"mvc.1.0.view", @"/Themes/Lepton/Components/MainMenu/Default.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\_ViewImports.cshtml"
using System.Globalization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 1 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton.Themes.Lepton.Components.MainMenu;

#line default
#line hidden
#nullable disable
#nullable restore
#line 3 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
using Volo.Abp.LeptonTheme.Management;

#line default
#line hidden
#nullable disable
#nullable restore
#line 4 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
using Volo.Abp.Settings;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"d2facc9ba60ebd9da913916c14213bdf5152c2c4", @"/Themes/Lepton/Components/MainMenu/Default.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"41d51eae9e844421817080a898274b7f478f62ad", @"/Themes/Lepton/_ViewImports.cshtml")]
    public class Themes_Lepton_Components_MainMenu_Default : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<MenuViewModel>
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 6 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
  
    var menuPlacement = await SettingProvider.GetOrNullAsync(LeptonThemeSettingNames.Layout.MenuPlacement);

#line default
#line hidden
#nullable disable
            WriteLiteral(@"<div class=""lp-sidebar collapse navbar-collapse d-lg-block"" id=""navbarSidebar"">
    <div class=""lp-sidebar-header"">
        <div class=""lp-toggle-sidebar"">
            <i class=""fa fa-align-left material-icons lp-open-icon""></i>
            <i class=""fa fa-align-justify material-icons lp-close-icon""></i>
        </div>
    </div>
    <div class=""lp-sidebar-wrapper"">
        <div class=""search-container"" style=""display: flex;"">
            <input type=""search"" id=""searchInput"" style=""margin:5px 10px; width: 100%; box-sizing: border-box;"" name=""searchInput""");
            BeginWriteAttribute("value", " value=\"", 899, "\"", 907, 0);
            EndWriteAttribute();
            WriteLiteral(" placeholder=\"Search...\" class=\"form-control\" oninput=\"searchMenu()\">\r\n           \r\n        </div>\r\n        <nav role=\"navigation\" class=\"lp-sidebar-navi\">\r\n            <ul>\r\n");
#nullable restore
#line 23 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
                 foreach (var menuItem in Model.Items)
                {
                    

#line default
#line hidden
#nullable disable
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
               Write(await Html.PartialAsync("~/Themes/Lepton/Components/MainMenu/_MenuItem.cshtml", menuItem));

#line default
#line hidden
#nullable disable
#nullable restore
#line 25 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\Volo.LeptonTheme\src\Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton\Themes\Lepton\Components\MainMenu\Default.cshtml"
                                                                                                              
                }

#line default
#line hidden
#nullable disable
            WriteLiteral("            </ul>\r\n        </nav>\r\n    </div>\r\n</div>");
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public ISettingProvider SettingProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<MenuViewModel> Html { get; private set; }
    }
}
#pragma warning restore 1591
