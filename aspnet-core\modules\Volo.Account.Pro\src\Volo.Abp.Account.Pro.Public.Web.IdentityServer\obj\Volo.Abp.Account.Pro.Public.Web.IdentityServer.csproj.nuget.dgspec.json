{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web.IdentityServer\\Volo.Abp.Account.Pro.Public.Web.IdentityServer.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\Volo.Abp.Account.Pro.Public.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\Volo.Abp.Account.Pro.Public.Application.Contracts.csproj", "projectName": "Volo.Abp.Account.Pro.Public.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\Volo.Abp.Account.Pro.Public.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Emailing": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\Volo.Abp.Account.Pro.Public.HttpApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\Volo.Abp.Account.Pro.Public.HttpApi.csproj", "projectName": "Volo.Abp.Account.Pro.Public.HttpApi", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\Volo.Abp.Account.Pro.Public.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\Volo.Abp.Account.Pro.Public.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Application.Contracts\\Volo.Abp.Account.Pro.Public.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "Owl.reCAPTCHA": {"target": "Package", "version": "[0.4.0, )"}, "Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.Pro.HttpApi": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web.IdentityServer\\Volo.Abp.Account.Pro.Public.Web.IdentityServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web.IdentityServer\\Volo.Abp.Account.Pro.Public.Web.IdentityServer.csproj", "projectName": "Volo.Abp.Account.Pro.Public.Web.IdentityServer", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web.IdentityServer\\Volo.Abp.Account.Pro.Public.Web.IdentityServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web.IdentityServer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\Volo.Abp.Account.Pro.Public.Web.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\Volo.Abp.Account.Pro.Public.Web.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "System.Security.Principal.Windows": {"target": "Package", "version": "[4.7.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.IdentityServer.Domain": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["portable-net45+win8+wp8+wpa81", "net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\Volo.Abp.Account.Pro.Public.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\Volo.Abp.Account.Pro.Public.Web.csproj", "projectName": "Volo.Abp.Account.Pro.Public.Web", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\Volo.Abp.Account.Pro.Public.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.Web\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\Volo.Abp.Account.Pro.Public.HttpApi.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Public.HttpApi\\Volo.Abp.Account.Pro.Public.HttpApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "Volo.Abp.AspNetCore.MultiTenancy": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Commercial": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.AutoMapper": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Emailing": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.AspNetCore": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.Pro.Domain": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Sms": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["portable-net45+win8+wp8+wpa81", "net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj", "projectName": "Volo.Abp.Account.Pro.Shared.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.Pro.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ldap": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}}}