using MasterDataModule.Agens;
using MasterDataModule.EmployeesTimeTrackings;
using MasterDataModule.Users;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.Record.Chart;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using OutboundModule.CheckDocuments;
using OutboundModule.DoPoManages;
using OutboundModule.GroupPrcvHistories;
using OutboundModule.TotalVolumes;
using OutboundModule.VehiclesRegistrations;
using SeaModule.ContainerInfos;
using ShareDataModule.ErtsRemoteTransitSheds;
using ShareDataModule.Flups;
using ShareDataModule.Shared;
using Spire.Xls;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.FileManagement;
using static InboundModule.Permissions.InboundModulePermissions;

namespace ReportModule.Outbound
{
    public class ReportAppService : ApplicationService, IReportAppService
    {
        private readonly MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationRepository _vehiclesRegistrationRepositoryMaster;
        private readonly ICheckDocumentsAppService _checkDocumentsAppService;
        private readonly IVehiclesRegistrationRepository _vehiclesRegistrationRepository;
        private readonly IDoPoManageRepository _doPoManageRepository;
        private readonly MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationAppService _vehiclesRegistrationAppService;
        private readonly EmployeesTimeTrackingAppService _employeesTimeTrackingAppService;
        private readonly IDailyTotalVolumeRepository _dailyTotalVolumecRepository;
        private readonly IAgenRepository _agenRepository;
        private readonly IUserRepository _userRepository;
        private readonly IFlupRepository _flupRepository;
        private readonly IContainerInfoAppService _containerInfoAppService;
        private readonly IGroupPrcvHistoryRepository _groupPrcvHistoryRepository;
        private readonly IErtsRemoteTransitShedRepository _ertsRemoteTransitShedRepository;
        protected IBlobContainer<FileManagementContainer> BlobContainer { get; }

        public ReportAppService(IBlobContainer<FileManagementContainer> blobContainer,
            ICheckDocumentsAppService checkDocumentsAppService,
            IVehiclesRegistrationRepository vehiclesRegistrationRepository,
            MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationAppService vehiclesRegistrationAppService,
            IDoPoManageRepository doPoManageRepository,
            MasterDataModule.VehiclesRegistrations.IVehiclesRegistrationRepository vehiclesRegistrationRepositoryMaster,
            EmployeesTimeTrackingAppService employeesTimeTrackingAppService,
            IDailyTotalVolumeRepository dailyTotalVolumecRepository,
            IUserRepository userRepository,
            IAgenRepository agenRepository,
            IFlupRepository flupRepository,
            IContainerInfoAppService containerInfoAppService,
            IGroupPrcvHistoryRepository groupPrcvHistoryRepository,
            IErtsRemoteTransitShedRepository ertsRemoteTransitShedRepository
            )
        {
            BlobContainer = blobContainer;
            _checkDocumentsAppService = checkDocumentsAppService;
            _vehiclesRegistrationRepository = vehiclesRegistrationRepository;
            _vehiclesRegistrationAppService = vehiclesRegistrationAppService;
            _doPoManageRepository = doPoManageRepository;
            _vehiclesRegistrationRepositoryMaster = vehiclesRegistrationRepositoryMaster;
            _employeesTimeTrackingAppService = employeesTimeTrackingAppService;
            _dailyTotalVolumecRepository = dailyTotalVolumecRepository;
            _userRepository = userRepository;
            _agenRepository = agenRepository;
            _flupRepository = flupRepository;
            _containerInfoAppService = containerInfoAppService;
            _groupPrcvHistoryRepository = groupPrcvHistoryRepository;
            _ertsRemoteTransitShedRepository = ertsRemoteTransitShedRepository;
        }

        // Báo cáo ca trực:
        public async Task<IRemoteStreamContent> DownloadDailyMiningReportAsync(DownloadDailyMiningReportInput input)
        {
            var lstTimeKeepings = (await _employeesTimeTrackingAppService.GetTimekeepingListAsync(new GetTimekeepingListInput
            {
                ImportExportType = "EXPORT",
                EttmOndate = input.p_Date
            }));
            // Read file template
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                ExcelWorksheet sheetBangChamCong = excelPackage.Workbook.Worksheets["Bang_cham_cong_ngay"];
                ExcelWorksheet sheetBaoCaoSanLuong = excelPackage.Workbook.Worksheets["Baocaosanluong"];
                
                FillDataToSheetBangChamCong(sheetBangChamCong, lstTimeKeepings, input.p_Date);
                await FillDataToSheetBaoCaoSanLuong(sheetBaoCaoSanLuong, input.p_Date, input.p_TimeRange);
                
                excelPackage.Save();
                stream = excelPackage.Stream;
            }
            return new RemoteStreamContent(stream);
        }

        // BC 2: List DO về kho
        public async Task<IRemoteStreamContent> DownloadListDOVeKhoAsync (DownloadListDOVeKhoInput input)
        {
            string dateFormat = input.p_FromDate + " - " + input.p_ToDate;

            string dateFromStr = $"{input.p_FromDate} {input.p_FromTime}";
            string dateToStr = $"{input.p_ToDate} {input.p_ToTime}";
            DateTime dateFrom = DateTime.MinValue;
            DateTime dateTo = DateTime.MinValue;
            try
            {
                dateFrom = DateTime.ParseExact(dateFromStr, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture);
                dateTo = DateTime.ParseExact(dateToStr, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            //var dateStart = CommonFunction.ConverDateStringToDate(input.p_FromDate);
            //var dateEnd = CommonFunction.ConverDateStringToDate(input.p_ToDate).AddDays(1).AddTicks(-1);
            var listShipmentDetails = (await _doPoManageRepository.GetQueryableListDoVeKhoAsync(arrivalALSBDateFrom: dateFrom, arrivalALSBDateTo: dateTo)).OrderBy(x => x.DateArrALSB).ThenBy(x => x.TruckNo).ToList();
            var listShipmentDetailsDaCan = listShipmentDetails.Where(x => x.DateGRALSB != null || (x.PltSeaAirType ?? string.Empty).Contains("MIX")).OrderBy(x=>x.DateArrALSB).ThenBy(x=>x.TruckNo).ToList();
            var listShipmentDetailsChuaCan = listShipmentDetails.Where(x => x.DateGRALSB == null && !(x.PltSeaAirType ?? string.Empty).Contains("MIX")).OrderBy(x => x.DateArrALSB).ThenBy(x => x.TruckNo).ToList();
            // Read file template 
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                ExcelWorksheet excelWorksheetAllDO = excelPackage.Workbook.Worksheets["ALL DO"];
                FillDataToSheetALLDO(excelWorksheetAllDO, listShipmentDetails, input);
                ExcelWorksheet excelWorksheetDaCan = excelPackage.Workbook.Worksheets["Đã cân"];
                FillDataToSheetDODaCan(excelWorksheetDaCan, listShipmentDetailsDaCan, input);
                ExcelWorksheet excelWorksheetChuaCan = excelPackage.Workbook.Worksheets["Chưa cân"];
                FillDataToSheetDOChuaCan(excelWorksheetChuaCan, listShipmentDetailsChuaCan, input);
                excelPackage.Save();
                stream = excelPackage.Stream;
            }

            return new RemoteStreamContent(stream);
        }
        public void FillDataToSheetALLDO(ExcelWorksheet excelWorksheet, List<ListShipmentLocationByDoForReport2> listShipmentDetails, DownloadListDOVeKhoInput input)
        {
            // Header info
            excelWorksheet.Cells["A4"].Value = $"Từ ngày {input.p_FromDate} lúc {input.p_FromTime} đến ngày {input.p_ToDate} lúc {input.p_ToTime}";
            excelWorksheet.Cells["O5"].Value = $"Ngày in: {DateTime.UtcNow:dd/MM/yyyy}"; // Ngày in
            
            var noNumber = 0;
            // Danh sách DO
            var indexStartRow = 7;
            foreach (var item in listShipmentDetails)
            {
                noNumber++;
                excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;                                        //1.Stt
                excelWorksheet.Cells[indexStartRow, 2].Value = item.DoNo;                                       //2.Số DO
                excelWorksheet.Cells[indexStartRow, 3].Value = item.PalletNo;                                   //3.Số Pallet
                excelWorksheet.Cells[indexStartRow, 4].Value = item.DateGRALSBString;                           //4.ALSB GR Time
                excelWorksheet.Cells[indexStartRow, 5].Value = item.Pcs;                                        //5.PCS
                excelWorksheet.Cells[indexStartRow, 6].Value = item.Gw;                                         //6.GW
                excelWorksheet.Cells[indexStartRow, 7].Value = item.PltDimL;                                    //7.L
                excelWorksheet.Cells[indexStartRow, 8].Value = item.PltDimW;                                    //8.W
                excelWorksheet.Cells[indexStartRow, 9].Value = item.PltDimH;                                    //9.H
                excelWorksheet.Cells[indexStartRow, 10].Value = $"{item.GRTimeInFactory:dd/MM/yyyy HH:mm}";     //10.Gr Time In Factory
                excelWorksheet.Cells[indexStartRow, 11].Value = $"{item.DatePDA:dd/MM/yyyy HH:mm}";             //11.Close Truck Factory
                excelWorksheet.Cells[indexStartRow, 12].Value = $"{item.DateOutFactory:dd/MM/yyyy HH:mm}";      //12.Time Out Factory
                excelWorksheet.Cells[indexStartRow, 13].Value = $"{item.DateArrALSB:dd/MM/yyyy HH:mm}";         //13.ALSB Date
                excelWorksheet.Cells[indexStartRow, 14].Value = item.Factory;                                   //14.Factory Warehouse
                excelWorksheet.Cells[indexStartRow, 15].Value = item.TruckPickUp;                               //15.Truck PickUP No                                                                    
                excelWorksheet.Cells[indexStartRow, 16].Value = item.Pcs;                                       //16.Pallet Number
                excelWorksheet.Cells[indexStartRow, 17].Value = item.Dest;                                      //17.DEST
                excelWorksheet.Cells[indexStartRow, 18].Value = (!item.PltSeaAirType.IsNullOrEmpty()) ? (item.PltSeaAirType.Contains("Mix") ? "Mix DO" : "") : "";  //18.Remark
                
                // Update Style
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                indexStartRow++;
            }
            // footer:
            excelWorksheet.Cells["B5"].Value = noNumber; // Đếm DO
            excelWorksheet.Cells[indexStartRow, 14].Value = "Người lập";
            excelWorksheet.Cells[indexStartRow, 14].Style.Font.Bold = true;
        }

        public void FillDataToSheetDODaCan(ExcelWorksheet excelWorksheet, List<ListShipmentLocationByDoForReport2> listShipmentDetailsDaCan, DownloadListDOVeKhoInput input)
        {
            // Header info
            excelWorksheet.Cells["A4"].Value = $"Từ ngày {input.p_FromDate} lúc {input.p_FromTime} đến ngày {input.p_ToDate} lúc {input.p_ToTime}";
            excelWorksheet.Cells["O5"].Value = $"Ngày in: {DateTime.UtcNow:dd/MM/yyyy}"; // Ngày in
            var noNumber = 0;
            // Danh sách DO
            var indexStartRow = 7;
            foreach (var item in listShipmentDetailsDaCan)
            {
                noNumber++;
                excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;                                        //1.Stt
                excelWorksheet.Cells[indexStartRow, 2].Value = item.DoNo;                                       //2.Số DO
                excelWorksheet.Cells[indexStartRow, 3].Value = item.PalletNo;                                   //3.Số Pallet
                excelWorksheet.Cells[indexStartRow, 4].Value = item.DateGRALSBString;                           //4.ALSB GR Time
                excelWorksheet.Cells[indexStartRow, 5].Value = item.Pcs;                                        //5.PCS
                excelWorksheet.Cells[indexStartRow, 6].Value = item.Gw;                                         //6.GW
                excelWorksheet.Cells[indexStartRow, 7].Value = item.PltDimL;                                    //7.L
                excelWorksheet.Cells[indexStartRow, 8].Value = item.PltDimW;                                    //8.W
                excelWorksheet.Cells[indexStartRow, 9].Value = item.PltDimH;                                    //9.H
                excelWorksheet.Cells[indexStartRow, 10].Value = $"{item.GRTimeInFactory:dd/MM/yyyy HH:mm}";     //10.Gr Time In Factory
                excelWorksheet.Cells[indexStartRow, 11].Value = $"{item.DatePDA:dd/MM/yyyy HH:mm}";             //11.Close Truck Factory
                excelWorksheet.Cells[indexStartRow, 12].Value = $"{item.DateOutFactory:dd/MM/yyyy HH:mm}";      //12.Time Out Factory
                excelWorksheet.Cells[indexStartRow, 13].Value = $"{item.DateArrALSB:dd/MM/yyyy HH:mm}";         //13.ALSB Date
                excelWorksheet.Cells[indexStartRow, 14].Value = item.Factory;                                   //14.Factory Warehouse
                excelWorksheet.Cells[indexStartRow, 15].Value = item.TruckPickUp;                               //15.Truck PickUP No                                                                    
                excelWorksheet.Cells[indexStartRow, 16].Value = item.Pcs;                                       //16.Pallet Number
                excelWorksheet.Cells[indexStartRow, 17].Value = item.Dest;                                      //17.DEST
                excelWorksheet.Cells[indexStartRow, 18].Value = (!item.PltSeaAirType.IsNullOrEmpty()) ? (item.PltSeaAirType.Contains("Mix") ? "Mix DO" : "") : "";  //18.Remark

                // Update Style
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                indexStartRow++;
            }
            // footer:
            excelWorksheet.Cells["B5"].Value = noNumber; // Đếm DO
            excelWorksheet.Cells[indexStartRow, 14].Value = "Người lập";
            excelWorksheet.Cells[indexStartRow, 14].Style.Font.Bold = true;
        }

        public void FillDataToSheetDOChuaCan(ExcelWorksheet excelWorksheet, List<ListShipmentLocationByDoForReport2> listShipmentDetailsChuaCan, DownloadListDOVeKhoInput input)
        {
            // Header info
            excelWorksheet.Cells["A4"].Value = $"Từ ngày {input.p_FromDate} lúc {input.p_FromTime} đến ngày {input.p_ToDate} lúc {input.p_ToTime}";
            excelWorksheet.Cells["O5"].Value = $"Ngày in: {DateTime.UtcNow:dd/MM/yyyy}"; // Ngày in
            var noNumber = 0;
            // Danh sách DO
            var indexStartRow = 7;
            foreach (var item in listShipmentDetailsChuaCan)
            {
                noNumber++;
                excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;                                        //1.Stt
                excelWorksheet.Cells[indexStartRow, 2].Value = item.DoNo;                                       //2.Số DO
                excelWorksheet.Cells[indexStartRow, 3].Value = item.PalletNo;                                   //3.Số Pallet
                excelWorksheet.Cells[indexStartRow, 4].Value = item.DateGRALSBString;                           //4.ALSB GR Time
                excelWorksheet.Cells[indexStartRow, 5].Value = item.Pcs;                                        //5.PCS
                excelWorksheet.Cells[indexStartRow, 6].Value = item.Gw;                                         //6.GW
                excelWorksheet.Cells[indexStartRow, 7].Value = item.PltDimL;                                    //7.L
                excelWorksheet.Cells[indexStartRow, 8].Value = item.PltDimW;                                    //8.W
                excelWorksheet.Cells[indexStartRow, 9].Value = item.PltDimH;                                    //9.H
                excelWorksheet.Cells[indexStartRow, 10].Value = $"{item.GRTimeInFactory:dd/MM/yyyy HH:mm}";     //10.Gr Time In Factory
                excelWorksheet.Cells[indexStartRow, 11].Value = $"{item.DatePDA:dd/MM/yyyy HH:mm}";             //11.Close Truck Factory
                excelWorksheet.Cells[indexStartRow, 12].Value = $"{item.DateOutFactory:dd/MM/yyyy HH:mm}";      //12.Time Out Factory
                excelWorksheet.Cells[indexStartRow, 13].Value = $"{item.DateArrALSB:dd/MM/yyyy HH:mm}";         //13.ALSB Date
                excelWorksheet.Cells[indexStartRow, 14].Value = item.Factory;                                   //14.Factory Warehouse
                excelWorksheet.Cells[indexStartRow, 15].Value = item.TruckPickUp;                               //15.Truck PickUP No                                                                    
                excelWorksheet.Cells[indexStartRow, 16].Value = item.Pcs;                                       //16.Pallet Number
                excelWorksheet.Cells[indexStartRow, 17].Value = item.Dest;                                      //17.DEST
                excelWorksheet.Cells[indexStartRow, 18].Value = (!item.PltSeaAirType.IsNullOrEmpty()) ? (item.PltSeaAirType.Contains("Mix") ? "Mix DO" : "") : "";  //18.Remark

                // Update Style
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 18].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                indexStartRow++;
            }
            // footer:
            excelWorksheet.Cells["B5"].Value = noNumber; // Đếm DO
            excelWorksheet.Cells[indexStartRow, 14].Value = "Người lập";
            excelWorksheet.Cells[indexStartRow, 14].Style.Font.Bold = true;
        }
        
        // BC 14: Sổ tổng hợp HHXK
        public async Task<IRemoteStreamContent> DownloadSoTongHopHHXKAsync(DownloadSoTongHopHHXKInput input)
        {
            string dateFormat = input.p_FromDate + " - " + input.p_ToDate;
            var transitDateStart = CommonFunction.ConverDateStringToDate(input.p_FromDate);
            var transitDateEnd = CommonFunction.ConverDateStringToDate(input.p_ToDate).AddDays(1).AddTicks(-1);
            var truckTransit = await _vehiclesRegistrationRepository.GetListTruckForCheckDocAsync(transitStartDate:transitDateStart,transitEndDate:transitDateEnd);
            // Read file template 
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                var sheetName = "DSTONG";
                ExcelWorksheet excelWorksheetDsTong = excelPackage.Workbook.Worksheets[sheetName];
                await FillDataToSheetDsTong(excelWorksheetDsTong, truckTransit);
                excelPackage.Save();
                stream = excelPackage.Stream;
            }

            return new RemoteStreamContent(stream);
        }


        public async Task FillDataToSheetDsTong(ExcelWorksheet excelWorksheet, List<ListCheckDoc> truckTransit)
        {
            try
            {
                // clear data:
                excelWorksheet.Cells["A11:Z1000"].Clear();
                // Header info
                excelWorksheet.Cells[7, 1].Value = $"Ngày {truckTransit[0].VhclLoadingLeftDate:dd} tháng {truckTransit[0].VhclLoadingLeftDate:MM} năm {truckTransit[0].VhclLoadingLeftDate:yyyy}";
                var sumMawb = 0;
                long sumPcs = 0;
                decimal sumGw = 0;
                var noNumber = 1;
                
                //NCTS Summary:
                var sumMawbNCTS = 0; long sumPcsNCTS = 0; decimal sumGwNCTS = 0; int countTruckNCTS = 0;
                //ACSV Summary:
                var sumMawbACSV = 0; long sumPcsACSV = 0; decimal sumGwACSV = 0; int countTruckACSV = 0;
                //ALSC Summary:
                var sumMawbALSC = 0; long sumPcsALSC = 0; decimal sumGwALSC = 0; int countTruckALSC = 0;

                // Danh sách hàng
                var indexStartRow = 11;
                foreach (var truck in truckTransit)
                {
                    //long totalPcs = 0;
                    //decimal totalGw = 0;

                    //long totalPcsNCTS = 0; decimal totalGwNCTS = 0;
                    //long totalPcsACSV = 0; decimal totalGwACSV = 0;
                    //long totalPcsALSC = 0; decimal totalGwALSC = 0;
                    string checkTruckTerminal = "";
                    var countSoLgTKHQ = 1;
                    // get truck detail
                    var truckTransitDetail = await _checkDocumentsAppService.GetDetailDocumentInfo(truck.Id);
                    var truckDetail = await _checkDocumentsAppService.GetGroundShippingListForReport14Async(truck.Id);
                    
                    foreach (var item in truckDetail)
                    {
                        var soTKHQ = item.AllCdNo;
                        // check số TKHQ
                        if (item.AllCdNo.Split("/").Length >= 2)
                        {
                            countSoLgTKHQ = item.AllCdNo.Split("/").Length;
                        }
                        excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;                            //Stt
                        excelWorksheet.Cells[indexStartRow, 2].Value = item.Mawb.Replace("-", "");          //Số AWB
                        excelWorksheet.Cells[indexStartRow, 3].Value = item.TotalPiecesOfMawb;              //Số kiện
                        excelWorksheet.Cells[indexStartRow, 4].Value = item.TotalGrossWeightOfMawb;         //GW (đã làm tròn)
                        excelWorksheet.Cells[indexStartRow, 5].Value = item.TotalVolumeWeightOfMawb;        //VW
                        excelWorksheet.Cells[indexStartRow, 6].Value = item.TotalChargableWeightOfMawb;     //CW
                        excelWorksheet.Cells[indexStartRow, 7].Value = item.Flight;                         //Số hiệu CB
                        excelWorksheet.Cells[indexStartRow, 8].Value = $"{item.FlightDate:dd/MM/yyyy}";     //ngày
                        excelWorksheet.Cells[indexStartRow, 9].Value = $"HAN/{item.Dest}";                  //Hành trình
                        excelWorksheet.Cells[indexStartRow, 10].Value =
                        item.PltGroupPalletNo.Contains("101") ? "Material" : "Mobiphone";               //Tên Hàng
                        excelWorksheet.Cells[indexStartRow, 11].Value = item.AllCdNo;                       //Tờ khai số
                        excelWorksheet.Cells[indexStartRow, 12].Value = countSoLgTKHQ;                      //Số lượng tờ khai
                        excelWorksheet.Cells[indexStartRow, 13].Value = $"{item.LabsDatewhencsrset:dd/MM/yyyy}"; //TG CNH (SLI Finish Date) 
                        excelWorksheet.Cells[indexStartRow, 14].Value = item.LabsTimewhencsrset.Replace(":", "").Substring(0, 4);          //TG CNH (SLI Finish Time)                                                                           
                        //$"{item.LabsLatestAcceptenceDate:dd-MMM}";                                        //Ngày nhập
                        excelWorksheet.Cells[indexStartRow, 15].Value = item.Forwarder;                     //FWD
                        excelWorksheet.Cells[indexStartRow, 16].Value = "";                                 //So BBBGHQ
                        excelWorksheet.Cells[indexStartRow, 17].Value = truck.TruckNo;                      //Biển số xe
                        if (!string.IsNullOrEmpty(truck.TransitDatetime))
                        {
                            excelWorksheet.Cells[indexStartRow, 18].Value = truck.TransitDatetime.Substring(truck.TransitDatetime.IndexOf(' ') + 1, 5).Replace(":", "");            //Thời gian xe chạy
                        }
                        excelWorksheet.Cells[indexStartRow, 19].Value = truckTransitDetail.SealSecurity;    //Seal niêm phong
                        excelWorksheet.Cells[indexStartRow, 20].Value = truckTransitDetail.SealCustom;      //Seal Hải quan
                        excelWorksheet.Cells[indexStartRow, 21].Value = truck.TransitDatetime.Split(' ')[0];//ALSB Out date
                        string maxApproveDateTime = (item.MaxApproveDatetime == null ? "" : DateTime.Parse(item.MaxApproveDatetime.ToString()).ToString("dd/MM/yyy HH:mm"));
                        if (!string.IsNullOrEmpty(maxApproveDateTime))
                        {
                            excelWorksheet.Cells[indexStartRow, 22].Value = maxApproveDateTime.Substring(maxApproveDateTime.IndexOf(' ') + 1, 5).Replace(":", "");   //CD Time
                        }
                        excelWorksheet.Cells[indexStartRow, 23].Value = item.PrintSliDateStr.Split(' ')[1].Replace(":",""); //RM
                        excelWorksheet.Cells[indexStartRow, 24].Value = item.Terminal;                      //Warehouse
                        excelWorksheet.Cells[indexStartRow, 25].Value = item.LabsGoodType;                  //Class
                        excelWorksheet.Cells[indexStartRow, 26].Value = item.LabsManifestRemarks;           //Remark
                        if (!string.IsNullOrEmpty(truck.ArrivedNBA))
                        {
                            excelWorksheet.Cells[indexStartRow, 27].Value = truck.ArrivedNBA.Substring(truck.ArrivedNBA.IndexOf(' ') + 1, 5);         //Arr NBA
                        }
                        excelWorksheet.Cells[indexStartRow, 28].Value = $"{item.FlightDate:dd/MM/yyyy}";     //Ngày bay
                        DateTime? flightScheduleDate;
                        if (item.BookFlightIsn.HasValue)
                        {
                            Flup flup = await _flupRepository.FirstOrDefaultAsync(p => p.Id.Equals(item.BookFlightIsn));
                            if (flup != null)
                            {
                                flightScheduleDate = CommonFunction.CombineDateAndTime(CommonFunction.ConverNumberToDate((int)flup.FlupScheduledDate), flup.FlupScheduledTime);
                                excelWorksheet.Cells[indexStartRow, 29].Value = flightScheduleDate.HasValue ? $"{flightScheduleDate:HHmm}" : $"{item.FlightDate:HHmm}";//Giờ bay
                                if(flup.FlupType == "CARGO")
                                {
                                    excelWorksheet.Cells[indexStartRow, 30].Value = "F";//Loại tàu bay
                                }
                                else
                                {
                                    excelWorksheet.Cells[indexStartRow, 30].Value = "P";//Loại tàu bay
                                }
                            }
                            else
                            {
                                excelWorksheet.Cells[indexStartRow, 29].Value = $"{item.FlightDate:HHmm}";     //Giờ bay
                            }
                        }    
                        // Update Style
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 30].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 30].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 30].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 30].Style.Border.Left.Style = ExcelBorderStyle.Thin;

                        indexStartRow++;
                        noNumber++;
                        sumMawb++;
                        sumPcs += item.TotalPiecesOfMawb;
                        sumGw += item.TotalGrossWeightOfMawb;
                        //sumGw += item.TotalGrossWeightOfMawb;
                        if (item.Terminal == "NCTS")
                        {
                            sumMawbNCTS++;// số lô 
                            sumPcsNCTS+= item.TotalPiecesOfMawb;
                            sumGwNCTS += item.TotalGrossWeightOfMawb;
                            checkTruckTerminal = "NCTS";
                        }
                        if (item.Terminal == "ACSV")
                        {
                            sumMawbACSV++;// số lô 
                            sumPcsACSV += item.TotalPiecesOfMawb;
                            sumGwACSV += item.TotalGrossWeightOfMawb;
                            checkTruckTerminal = "ACSV";
                        }
                        if (item.Terminal == "ALSC")
                        {
                            sumMawbALSC++;// số lô 
                            sumPcsALSC += item.TotalPiecesOfMawb;
                            sumGwALSC += item.TotalGrossWeightOfMawb;
                            checkTruckTerminal = "ALSC";
                        }
                    }
                    //sumPcs += totalPcs;
                    if (checkTruckTerminal == "NCTS")
                    {
                        countTruckNCTS++;
                    }
                    if (checkTruckTerminal == "ACSV")
                    {
                        countTruckACSV++;
                    }
                    if (checkTruckTerminal == "ALSC")
                    {
                        countTruckALSC++;
                    }

                }
                
                excelWorksheet.Cells[9, 9].Value = $"Tổng số: {truckTransit.Count} xe / {sumMawb} lô / {sumPcs} kiện / {sumGw} kgs";
                excelWorksheet.Cells[7, 11].Value = $"Kho NCTS: {countTruckNCTS} xe / {sumMawbNCTS} lô / {sumPcsNCTS} kiện / {sumGwNCTS} kgs";
                excelWorksheet.Cells[8, 11].Value = $"Kho ACSV: {countTruckACSV} xe / {sumMawbACSV} lô / {sumPcsACSV} kiện / {sumGwACSV} kgs";
                excelWorksheet.Cells[9, 11].Value = $"Kho ALSC: {countTruckALSC} xe / {sumMawbALSC} lô / {sumPcsALSC} kiện / {sumGwALSC} kgs";
            }
            catch (System.Exception)
            {

                throw;
            }
        }

        public async Task<IRemoteStreamContent> DownloadBBNhanVaVanChuyenHangAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            var now = DateTime.Now;
            DateTime? firstDateOfMonth = new DateTime(now.Year, now.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
            DateTime? startOfDate = DateTime.Now;
            DateTime? endOfDate = DateTime.Now;
            DateTime? startOfDateCalculate = DateTime.Now;
            string monthReport = $"{now:MM}";
            string dateReport = $"{now:dd.MM.yyyy}";
            if (DateTime.TryParseExact(input.p_FromDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateFrom))
            {
                startOfDate = dateFrom;
                startOfDateCalculate = dateFrom.AddTicks(-1);
                firstDateOfMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
                monthReport = $"{dateFrom:MM}";
                dateReport = $"{dateFrom:dd.MM.yyyy}";
            }
            if (DateTime.TryParseExact(input.p_ToDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTo))
            {
                endOfDate = dateTo.AddDays(1).AddTicks(-1);
                firstDateOfMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
                monthReport = $"{dateFrom:MM}";
                dateReport = $"{dateFrom:dd.MM.yyyy}";
            }
            var truckUnloadingReport = await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(startOfDate, endOfDate, "EXPORT", "PICK UP");
            var truckUnloadingsCalculate = await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(firstDateOfMonth, startOfDateCalculate, "EXPORT", "PICK UP");
            var lstDataReport = truckUnloadingReport.ToList().Where(x => x.UnloadingArrivalDate != null && x.Factory.Contains("SEV")).OrderBy(x => x.UnloadingArrivalDate).ThenBy(x => x.LoadingLeftDate);
            int dem = truckUnloadingsCalculate.ToList().Where(x => x.Factory.Contains("SEV")).Count(); // số xe đã unload từ đầu tháng đến startOfDate
            if (input.p_Vendor != "SEV")
            {
                lstDataReport = truckUnloadingReport.ToList().Where(x => x.UnloadingArrivalDate != null && !x.Factory.Contains("SEV")).OrderBy(x => x.UnloadingArrivalDate).ThenBy(x => x.LoadingLeftDate);
                dem = truckUnloadingsCalculate.ToList().Where(x => !x.Factory.Contains("SEV")).Count();
            }
            var memoryStream = new MemoryStream();
            var archive = new System.IO.Compression.ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true);
            foreach (var item in lstDataReport)
            {
                // Tạo stream cho từng file Excel
                var excelStream = new MemoryStream();
                var pdfStream = new MemoryStream();
                // Đọc file template
                var templateStream = await BlobContainer.GetAsync(input.FileId);
                dem++;
                var soBienBan = dem + "-" + monthReport + "-" + item.VehicRegNo + "-" + dateReport;
                using (var excelPackage = new ExcelPackage(templateStream))
                {
                    ExcelWorksheet excelWorksheet = excelPackage.Workbook.Worksheets[0];
                    await FillDataToBBNhanVaVanChuyenHang(excelWorksheet, item, soBienBan);

                    // Lưu file Excel vào MemoryStream
                    excelPackage.SaveAs(excelStream);
                }
                excelStream.Seek(0, SeekOrigin.Begin);
                System.IO.Compression.ZipArchiveEntry zipEntry;
                if (input.p_ExportType == "PDF")
                {
                    // convert to PDF
                    Workbook workbook = new Workbook();
                    workbook.LoadFromStream(excelStream);
                    // 2. Chuyển đổi sang PDF
                    workbook.SaveToStream(pdfStream, Spire.Xls.FileFormat.PDF);
                    pdfStream.Seek(0, SeekOrigin.Begin);
                    zipEntry = archive.CreateEntry($"{soBienBan}.pdf");
                }
                else
                {
                    zipEntry = archive.CreateEntry($"{soBienBan}.xlsx");
                }
                using (var entryStream = zipEntry.Open())
                {
                    if (input.p_ExportType == "PDF")
                    {
                        await pdfStream.CopyToAsync(entryStream);
                    }
                    else
                    {
                        await excelStream.CopyToAsync(entryStream);
                    }
                }
            }
            // Đóng archive để hoàn tất quá trình nén
            archive.Dispose();
            // Đặt con trỏ về đầu để trả về file ZIP
            memoryStream.Seek(0, SeekOrigin.Begin);
            return new RemoteStreamContent(memoryStream);
        }

        // Biên bản nhận và vận chuyển hàng theo DO (màn hình Outbound > Truck Unloading > Detail)
        public async Task<IRemoteStreamContent> DownloadBBNhanHangDOAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // Read file template 
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                ExcelWorksheet excelWorksheetDsTong = excelPackage.Workbook.Worksheets[0];
                
                var truckUnloading = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId: input.p_TruckId, type: "EXPORT", truckType: "PICK UP")).ToList()[0];
                var dateUnload = truckUnloading.UnloadingArrivalDate.Value;
                string soBienBan = await CountTruckUnloadingForReport(truckUnloading.Id);
                
                

                await FillDataToBBNhanVaVanChuyenHang(excelWorksheetDsTong, truckUnloading, soBienBan);
                excelPackage.Save();
                stream = excelPackage.Stream;
                if (input.p_ExportType == "PDF")
                {
                    // convert to PDF
                    Workbook workbook = new Workbook();
                    workbook.LoadFromStream(stream);
                    // 2. Chuyển đổi sang PDF
                    workbook.SaveToStream(stream, Spire.Xls.FileFormat.PDF);
                }
            }
            return new RemoteStreamContent(stream);
        }
       
        public async Task FillDataToBBNhanVaVanChuyenHang(ExcelWorksheet excelWorksheet, MasterDataModule.VehiclesRegistrations.VehiclesUnLoading truckUnloadings, string soBienBan)
        {
            try
            {
                var historyCloseTruck = (await _agenRepository.GetListHistoryAsync(truckUnloadings.Id));
                var employeeCloseTruck = historyCloseTruck.Where(x => x.Remark.ToUpper().Contains("CLOSE TRUCK")).FirstOrDefault().Employee;
                var picUser = _userRepository.FindAsync(x => x.IsDeleted == false && x.UserName.ToUpper() == employeeCloseTruck.ToUpper()).Result;
                if (truckUnloadings.UnloadingArrivalDate != null)
                {
                    var vehicleDetail = await _vehiclesRegistrationAppService.GetAsync(truckUnloadings.Id);
                    var lstDoPoByVehicle = await _doPoManageRepository.GetPoDoByVehicleIsnListAsync(vehicleIsn: truckUnloadings.Id);
                    var finishUnloadingTime = lstDoPoByVehicle.Max(p => p.Date);
                    var indexStartRow = 11;
                    var noNumber = 1;
                    int? sumPCS = 0;

                    // clear data:
                    excelWorksheet.Cells["A11:Z111"].Clear();
                    excelWorksheet.Cells["A11:Z111"].Style.Font.Size = 12;

                    // Header info
                    excelWorksheet.Cells["C4"].Value = $"Tại địa điểm: {truckUnloadings.FactoryWHPickup}";
                    excelWorksheet.Cells["G5"].Value = soBienBan;
                    excelWorksheet.Cells["A5"].Value = $"Ngày {truckUnloadings.UnloadingArrivalDate:dd} tháng {truckUnloadings.UnloadingArrivalDate:MM} năm {truckUnloadings.UnloadingArrivalDate:yyyy}";
                    excelWorksheet.Cells["G6"].Value = $"Biển số xe: {truckUnloadings.VehicRegNo}";
                    excelWorksheet.Cells["A6"].Value = $"Thời gian bắt đầu chất xếp hàng lên xe: {truckUnloadings.LoadingArrivalDatetime.Split(' ')[1]}";
                    excelWorksheet.Cells["G7"].Value = $"Số seal AN SEV (nếu có): ...............";
                    excelWorksheet.Cells["A7"].Value = "Thời gian đóng cửa thùng xe: " + (finishUnloadingTime != null ? $"{finishUnloadingTime:HH:mm}" : "");
                    excelWorksheet.Cells["A8"].Value = "Thời gian xe rời điểm nhận hàng: " + (!vehicleDetail.LoadingVehicleClosedDatetime.IsNullOrEmpty() ? vehicleDetail.LoadingVehicleClosedDatetime.Split(' ')[0] : "");
                    excelWorksheet.Cells["G8"].Value = $"Số Seal ALSB: {vehicleDetail.VhclSealNumber}";

                    // Danh sách hàng
                    
                    foreach (var item in lstDoPoByVehicle)
                    {
                        // Update Style Bảng
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;

                        excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;// 1.STT
                        excelWorksheet.Cells[indexStartRow, 2].Value = item.PoNumber;// 2.Số DO
                        excelWorksheet.Cells[indexStartRow, 3].Value = item.PiecesLoaded ?? 1;//item.ReceivedPieces;// 3.Số kiện
                        excelWorksheet.Cells[indexStartRow, 4].Value = !item.Time.IsNullOrEmpty() ? item.Time.Split(' ')[1] : "";// 4.Thời gian
                        excelWorksheet.Cells[indexStartRow, 5].Value = "";// 5.Trọng lượng
                        excelWorksheet.Cells[indexStartRow, 6].Value = item.Dest;// 6.Điểm đến
                        excelWorksheet.Cells[indexStartRow, 7].Value = "";// 7.Kích thước
                        excelWorksheet.Cells[indexStartRow, 8].Value = "";// item.Remark;// 8.Ghi chú
                        sumPCS += item.ReceivedPieces;
                        indexStartRow++;
                        noNumber++;
                    }
                    // Tổng:
                    excelWorksheet.Cells[indexStartRow, 2].Value = "Tổng";
                    excelWorksheet.Cells[indexStartRow, 3].Value = sumPCS;// Tổng PCS
                    // Update Style Bảng
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    // Ký tên
                    indexStartRow += 2;
                    excelWorksheet.Cells[indexStartRow, 1].Value = "Nhân viên khai thác nhận hàng tại nhà máy";
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;

                    indexStartRow++;
                    excelWorksheet.Cells[indexStartRow, 1].Value = "(ký và ghi rõ họ tên)";
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Italic = true;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;

                    indexStartRow += 2;
                    excelWorksheet.Cells[indexStartRow, 1].Value = picUser.Name;//CurrentUser.Name;
                    
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 2].Merge = true;

                    indexStartRow++;
                    excelWorksheet.Cells[indexStartRow, 4].Value = "Thời gian rời cổng nhà máy";
                    excelWorksheet.Cells[indexStartRow, 4].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 4].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 4, indexStartRow, 6].Merge = true;


                    excelWorksheet.Cells[indexStartRow, 7].Value = "Thời gian xe về đến ALSB: " + (!vehicleDetail.VhclUnloadingArrivalDateStr.IsNullOrEmpty() ? vehicleDetail.VhclUnloadingArrivalDateStr.Split(' ')[1] : "");
                    excelWorksheet.Cells[indexStartRow, 7].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 7].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 7, indexStartRow, 8].Merge = true;

                    indexStartRow++;
                    excelWorksheet.Cells[indexStartRow, 4].Value = (!vehicleDetail.VhclLoadingLeftDateStr.IsNullOrEmpty() ? vehicleDetail.VhclLoadingLeftDateStr.Split(' ')[1] : "");
                    excelWorksheet.Cells[indexStartRow, 4].Style.Font.Italic = true;
                    excelWorksheet.Cells[indexStartRow, 4].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 4, indexStartRow, 6].Merge = true;

                }
            }
            catch (Exception)
            {

                throw;
            }
        }

        public async Task<IRemoteStreamContent> DownloadBBCanHangAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            var now = DateTime.Now;
            DateTime? firstDateOfMonth = new DateTime(now.Year, now.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
            DateTime? startOfDate = DateTime.Now;
            DateTime? endOfDate = DateTime.Now;
            DateTime? startOfDateCalculate = DateTime.Now;
            string monthReport = $"{now:MM}";
            string dateReport = $"{now:dd.MM.yyyy}";
            if (DateTime.TryParseExact(input.p_FromDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateFrom))
            {
                startOfDate = dateFrom;
                startOfDateCalculate = dateFrom.AddTicks(-1);
                firstDateOfMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
                monthReport = $"{dateFrom:MM}";
                dateReport = $"{dateFrom:dd.MM.yyyy}";
            }
            if (DateTime.TryParseExact(input.p_ToDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTo))
            {
                endOfDate = dateTo.AddDays(1).AddTicks(-1);
                firstDateOfMonth = new DateTime(dateFrom.Year, dateFrom.Month, 1, 0, 0, 0); //ngày đầu tiên của tháng
                monthReport = $"{dateFrom:MM}";
                dateReport = $"{dateFrom:dd.MM.yyyy}";
            }
            var truckUnloadingReport = await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(startOfDate, endOfDate, "EXPORT", "PICK UP");
            var truckUnloadingsCalculate = await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(firstDateOfMonth, startOfDateCalculate, "EXPORT", "PICK UP");

            var lstDataReport = truckUnloadingReport.ToList().Where(x => x.UnloadingArrivalDate != null && x.Factory.Contains("SEV")).OrderBy(x => x.UnloadingArrivalDate).ThenBy(x => x.LoadingLeftDate);
            int dem = truckUnloadingsCalculate.ToList().Where(x => x.Factory.Contains("SEV")).Count(); // số xe đã unload từ đầu tháng đến startOfDate
            if (input.p_Vendor != "SEV")
            {
                lstDataReport = truckUnloadingReport.ToList().Where(x => x.UnloadingArrivalDate != null && !x.Factory.Contains("SEV")).OrderBy(x => x.UnloadingArrivalDate).ThenBy(x => x.LoadingLeftDate);
                dem = truckUnloadingsCalculate.ToList().Where(x => !x.Factory.Contains("SEV")).Count();
            }
            var memoryStream = new MemoryStream();
            var archive = new System.IO.Compression.ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true);
            foreach (var item in lstDataReport)
            {
                // Tạo stream cho từng file Excel
                var excelStream = new MemoryStream();
                var pdfStream = new MemoryStream();
                // Đọc file template
                var templateStream = await BlobContainer.GetAsync(input.FileId);
                dem++;
                var soBienBan = dem + "-" + monthReport + "-" + item.VehicRegNo + "-" + dateReport;
                using (var excelPackage = new ExcelPackage(templateStream))
                {
                    ExcelWorksheet excelWorksheet = excelPackage.Workbook.Worksheets[0];
                    await FillDataToBBCanHang(excelWorksheet, item, soBienBan);

                    // Lưu file Excel vào MemoryStream
                    excelPackage.SaveAs(excelStream);
                }
                excelStream.Seek(0, SeekOrigin.Begin);
                System.IO.Compression.ZipArchiveEntry zipEntry;
                if (input.p_ExportType == "PDF")
                {
                    // convert to PDF
                    Workbook workbook = new Workbook();
                    workbook.LoadFromStream(excelStream);
                    // 2. Chuyển đổi sang PDF
                    workbook.SaveToStream(pdfStream, Spire.Xls.FileFormat.PDF);
                    pdfStream.Seek(0, SeekOrigin.Begin);
                    zipEntry = archive.CreateEntry($"{soBienBan}.pdf");
                }
                else
                {
                    zipEntry = archive.CreateEntry($"{soBienBan}.xlsx");
                }
                using (var entryStream = zipEntry.Open())
                {
                    if (input.p_ExportType == "PDF")
                    {
                        await pdfStream.CopyToAsync(entryStream);
                    }
                    else
                    {
                        await excelStream.CopyToAsync(entryStream);
                    }
                }
            }
            // Đóng archive để hoàn tất quá trình nén
            archive.Dispose();
            // Đặt con trỏ về đầu để trả về file ZIP
            memoryStream.Seek(0, SeekOrigin.Begin);
            return new RemoteStreamContent(memoryStream);
        }

        // Biên bản cân hàng xuất theo 1 file (không zip) (màn hình Outbound > Truck Unloading > Detail)
        public async Task<IRemoteStreamContent> DownloadBBCanHangDetailAsync(DownloadBBNhanVaVanChuyenHangInput input)
        {
            // Read file template 
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                ExcelWorksheet excelWorksheetDsTong = excelPackage.Workbook.Worksheets[0];

                var truckUnloading = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId: input.p_TruckId, type: "EXPORT", truckType: "PICK UP")).ToList()[0];
                var dateUnload = truckUnloading.UnloadingArrivalDate.Value;
                string soBienBan = await CountTruckUnloadingForReport(truckUnloading.Id);
                await FillDataToBBCanHang(excelWorksheetDsTong, truckUnloading, soBienBan);
                excelPackage.Save();
                stream = excelPackage.Stream;
                if (input.p_ExportType == "PDF")
                {
                    // convert to PDF
                    Workbook workbook = new Workbook();
                    workbook.LoadFromStream(stream);
                    // 2. Chuyển đổi sang PDF
                    workbook.SaveToStream(stream, Spire.Xls.FileFormat.PDF);
                }
            }
            return new RemoteStreamContent(stream);
        }
        
        public async Task FillDataToBBCanHang(ExcelWorksheet excelWorksheet, MasterDataModule.VehiclesRegistrations.VehiclesUnLoading truckUnloadings, string soBienBan)
        {
            try
            {
                if (truckUnloadings.UnloadingArrivalDate != null)
                {
                    var vehicleDetail = await _vehiclesRegistrationAppService.GetAsync(truckUnloadings.Id);
                    var lstDoPoByVehicle = await _doPoManageRepository.GetPoDoByVehicleIsnListAsync(vehicleIsn: truckUnloadings.Id);
                    var indexStartRow = 8;
                    var noNumber = 1;
                    int? sumPCS = 0;
                    decimal? sumGw = 0;

                    // clear data:
                    excelWorksheet.Cells["A8:Z111"].Clear();
                    excelWorksheet.Cells["A8:Z111"].Style.Font.Size = 12;

                    // Header info
                    //excelWorksheet.Cells["A4"].Value = $"Tại địa điểm: {truckUnloadings.FactoryWHPickup}";
                    excelWorksheet.Cells["A5"].Value = $"Ngày {truckUnloadings.UnloadingArrivalDate:dd} tháng {truckUnloadings.UnloadingArrivalDate:MM} năm {truckUnloadings.UnloadingArrivalDate:yyyy}";
                    excelWorksheet.Cells["G5"].Value = $"Số xe: {truckUnloadings.VehicRegNo}";
                    excelWorksheet.Cells["G6"].Value = soBienBan;

                    // Danh sách hàng
                    foreach (var item in lstDoPoByVehicle)
                    {
                        // Update Style Bảng
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;

                        excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;// 1.STT
                        excelWorksheet.Cells[indexStartRow, 2].Value = item.PoNumber;// 2.Số DO
                        excelWorksheet.Cells[indexStartRow, 3].Value = item.ReceivedPieces;// 3.Số kiện
                        //excelWorksheet.Cells[indexStartRow, 4].Value = !item.Time.IsNullOrEmpty() ? item.Time.Split(' ')[1] : "";// 4.Thời gian
                        excelWorksheet.Cells[indexStartRow, 4].Value = item.ReceivedDate.HasValue ? $"{item.ReceivedDate:HH:mm}" : "";// 4.Thời gian
                        excelWorksheet.Cells[indexStartRow, 5].Value = item.ReceivedWeight;// 5.Trọng lượng
                        excelWorksheet.Cells[indexStartRow, 6].Value = item.Dest;// 6.Điểm đến
                        excelWorksheet.Cells[indexStartRow, 7].Value = item.DIM;// 7.Kích thước
                        excelWorksheet.Cells[indexStartRow, 8].Value = item.Remark;// 8.Ghi chú
                        sumPCS += item.ReceivedPieces;
                        sumGw += item.ReceivedWeight;
                        indexStartRow++;
                        noNumber++;
                    }
                    // Tổng:
                    excelWorksheet.Cells[indexStartRow, 2].Value = "Tổng";
                    excelWorksheet.Cells[indexStartRow, 3].Value = sumPCS;// Tổng PCS
                    excelWorksheet.Cells[indexStartRow, 5].Value = sumGw;// Tổng Gw

                    // Update Style Bảng
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.Font.Bold = true;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    // Ký tên
                    indexStartRow += 2;
                    excelWorksheet.Cells[indexStartRow, 1].Value = "Nhân viên cân hàng tại ALSB";
                    excelWorksheet.Cells[indexStartRow, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 3].Merge = true;

                    excelWorksheet.Cells[indexStartRow, 6].Value = "Thời gian xe về đến ALSB: " + (!vehicleDetail.VhclUnloadingArrivalDateStr.IsNullOrEmpty() ? vehicleDetail.VhclUnloadingArrivalDateStr.Split(' ')[1] : "");
                    excelWorksheet.Cells[indexStartRow, 6].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 6, indexStartRow, 8].Merge = true;

                    indexStartRow++;
                    excelWorksheet.Cells[indexStartRow, 1].Value = "(ký và ghi rõ họ tên)";
                    excelWorksheet.Cells[indexStartRow, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 3].Merge = true;

                    excelWorksheet.Cells[indexStartRow, 6].Value = "Thời gian dỡ xong hàng tại ALSB: " + (!vehicleDetail.VehicleCompleteDateTime.IsNullOrEmpty() ? vehicleDetail.VehicleCompleteDateTime.Split(' ')[0] : "");
                    excelWorksheet.Cells[indexStartRow, 6].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 6, indexStartRow, 8].Merge = true;

                    indexStartRow += 5;
                    excelWorksheet.Cells[indexStartRow, 1].Value = CurrentUser.Name.ToUpper();
                    excelWorksheet.Cells[indexStartRow, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    excelWorksheet.Cells[indexStartRow, 1].Style.Font.Size = 12;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 3].Merge = true;
                }
            }
            catch (Exception)
            {

                throw;
            }
        }
        
        /// <summary>
        /// Tính toán số lượng xe trong tháng có thời gian unloading trước xe truyền vào
        /// </summary>
        /// <param name="truckId">Id của xe truyền vào</param>
        /// <returns>Số lượng xe unloading</returns>
        public async Task<string> CountTruckUnloadingForReport(long truckId)
        {
            var truckUnload = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId: truckId,type:"EXPORT",truckType:"PICK UP")).ToList()[0];
            var dateUnload = truckUnload.UnloadingArrivalDate.Value;// thời điểm xe unload:
            var vendor = truckUnload.Factory;// vendor có phải SEV hay không:
            DateTime firtDayOfMonth = new DateTime(dateUnload.Year, dateUnload.Month, 1, 0, 0, 0);// ngày đầu tháng

            var countTruckUnloadingBefore = (await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(firtDayOfMonth, dateUnload, "EXPORT", "PICK UP")).ToList();
            int count = 0;// đếm số xe theo vendor:
            if (vendor.Contains("SEV"))
            {
                count = countTruckUnloadingBefore.Where(x => x.UnloadingArrivalDate != null && x.Factory.Contains("SEV")).Count();
            }
            else
            {
                count = countTruckUnloadingBefore.Where(x => x.UnloadingArrivalDate != null && !x.Factory.Contains("SEV")).Count();
            }
            string result = count + "-" + $"{dateUnload:MM}" + "-" + truckUnload.VehicRegNo + "-" + $"{dateUnload:dd.MM.yyyy}";
            return result;
        }
        
        public void FillDataToSheetBangChamCong(ExcelWorksheet excelWorksheet, List<TimekeepingListDto> lstTimeKeepings, string p_Date)
        {
            try
            {
                var indexStartRow = 4;
                var noNumber = 1;
                // clear data:
                excelWorksheet.Cells["A4:Z111"].Clear();
                excelWorksheet.Cells["A4:Z111"].Style.Font.Size = 12;
                // Header info
                excelWorksheet.Cells["H1"].Value = $"BẢNG CHẤM CÔNG KTX NGÀY {p_Date.Replace('/','-')}";
                // Danh sách nhân viên
                foreach (var item in lstTimeKeepings)
                {
                    // Update Style Bảng
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 24].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 24].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 24].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    excelWorksheet.Cells[indexStartRow, 1, indexStartRow, 24].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    // Fill data
                    excelWorksheet.Cells[indexStartRow, 1].Value = noNumber;// A.STT
                    excelWorksheet.Cells[indexStartRow, 2].Value = item.EttmEmpName;// B.Họ tên
                    excelWorksheet.Cells[indexStartRow, 3].Value = item.EmpTitle;// C.Chức danh
                    
                    var timekeepingItems = item.TimekeepingItems;
                    int dem = 4;
                    foreach (var timekeepingItem in timekeepingItems)
                    {
                        excelWorksheet.Cells[indexStartRow, dem++].Value = timekeepingItem.JobName.Split('|')[0].Trim(); // D.Mã vị trí n
                        excelWorksheet.Cells[indexStartRow, dem++].Value = timekeepingItem.Start;// E.Giờ bắt đầu n
                        excelWorksheet.Cells[indexStartRow, dem++].Value = timekeepingItem.Finish;// F.Giờ kết thúc n
                    }
                    excelWorksheet.Cells[indexStartRow, 23].Value = item.EttmRemarks;// Giải trình lý do
                    indexStartRow++;
                    noNumber++;
                }
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        public async Task FillDataToSheetBaoCaoSanLuong(ExcelWorksheet excelWorksheet, string p_Date, string p_TimeRange)
        {
            try
            {
                var now = DateTime.Now;
                int dem = 0;
                // clear data:
                excelWorksheet.Cells["C6:F22"].Clear();
                // Header info
                excelWorksheet.Cells["D2"].Value = $"Ngày {p_Date.Split('/')[0]} Tháng {p_Date.Split('/')[1]} Năm {p_Date.Split('/')[2]}";

                
                DateTime dateFrom = DateTime.MinValue;
                DateTime dateTo = DateTime.MinValue;
                
                if (!string.IsNullOrWhiteSpace(p_Date))
                {
                    string p_DateTime = p_Date + " 07:00";
                    if (p_TimeRange == "ALL")
                    {
                        dateFrom = DateTime.ParseExact(p_DateTime, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None);
                        dateTo = dateFrom.AddDays(1);
                    }
                    else
                    {
                        dateFrom = DateTime.ParseExact(p_DateTime, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None);
                        dateTo = dateFrom.AddHours(12);
                    }
                }

                var lstTruckUnloading = await _vehiclesRegistrationRepositoryMaster.GetQueryVehiclesUnLoadingExportForReport(dateFrom, dateTo, "EXPORT", "PICK UP");
                // A. Sản lượng:
                //1. Nhận hàng tại SEV:
                var lstTruckUnloadingSEV = lstTruckUnloading.ToList().Where(x => x.UnloadingArrivalDate != null && x.Factory.Contains("SEV"));
                excelWorksheet.Cells[6, dem + 3].Value = lstTruckUnloadingSEV.Count(); //số chuyến xe
                excelWorksheet.Cells[6, dem + 4].Value = 0; //số lô hàng
                excelWorksheet.Cells[6, dem + 5].Value = lstTruckUnloadingSEV.Sum(x=>x.PiecesScale); //số kiện hàng
                excelWorksheet.Cells[6, dem + 6].Value = lstTruckUnloadingSEV.Sum(x=>x.WeightScale); // trọng lượng
                
                //2. Nhận hàng từ vendor:
                var lstTruckUnloadingVendor = lstTruckUnloading.ToList().Where(x => x.UnloadingArrivalDate != null && !x.Factory.Contains("SEV"));
                excelWorksheet.Cells[7, dem + 3].Value = lstTruckUnloadingVendor.Count(); //số chuyến xe
                excelWorksheet.Cells[7, dem + 4].Value = 0; //số lô hàng
                excelWorksheet.Cells[7, dem + 5].Value = lstTruckUnloadingVendor.Sum(x => x.PiecesScale); //số kiện hàng
                excelWorksheet.Cells[7, dem + 6].Value = lstTruckUnloadingVendor.Sum(x => x.WeightScale); // trọng lượng
                
                //3. Nhập hàng vào kho:
                excelWorksheet.Cells[8, dem + 3].Value = lstTruckUnloading.Count(); //số chuyến xe
                excelWorksheet.Cells[8, dem + 4].Value = 0; //số lô hàng
                excelWorksheet.Cells[8, dem + 5].Value = lstTruckUnloading.Sum(x => x.PiecesScale); //số kiện hàng
                excelWorksheet.Cells[8, dem + 6].Value = lstTruckUnloading.Sum(x => x.WeightScale); // trọng lượng

                if (p_TimeRange == "ALL")
                {
                    p_TimeRange = "19h-7h";
                }
                else
                {
                    p_TimeRange = "7h-19h";
                }
                var lstDailyTotalVolumes = await _dailyTotalVolumecRepository.GetDailyTotalItemBy(p_Date, p_TimeRange);

                foreach (var dailyItem in lstDailyTotalVolumes)
                {
                    if (dailyItem.Order == 4)
                    {
                        //4. Xuất hàng đi Thái Nguyên:
                        excelWorksheet.Cells[9, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[9, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[9, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[9, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 5)
                    {
                        //5. Xuất hàng recall  SEV,Vendor
                        excelWorksheet.Cells[10, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[10, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[10, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[10, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 6)
                    {
                        //6. Bàn giao HTNS
                        excelWorksheet.Cells[11, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[11, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[11, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[11, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 7)
                    {
                        //7. Dịch vụ nâng hạ hàng vendor tại SEV
                        excelWorksheet.Cells[12, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[12, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[12, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[12, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 9)
                    {
                        //9. Chất cont tại SEV:
                        excelWorksheet.Cells[14, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[14, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[14, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[14, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 13)
                    {
                        //13. Dịch vụ gia cố:
                        excelWorksheet.Cells[18, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[18, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[18, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[18, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                    if (dailyItem.Order == 18)
                    {
                        //18. Bất thường hàng hóa:
                        excelWorksheet.Cells[23, dem + 3].Value = dailyItem.TruckCount; //số chuyến xe
                        excelWorksheet.Cells[23, dem + 4].Value = dailyItem.AwbCount; //số lô hàng
                        excelWorksheet.Cells[23, dem + 5].Value = dailyItem.PcsCount; //số kiện hàng
                        excelWorksheet.Cells[23, dem + 6].Value = dailyItem.GwCount; // trọng lượng
                    }
                   

                }
                //8. Chất cont tại ALSB:
                var listTruckSea = await _containerInfoAppService.GetListTruckSeaForReportAsync(dateFrom, dateTo, 0);
                excelWorksheet.Cells[13, dem + 3].Value = listTruckSea.Count(); //số chuyến xe
                excelWorksheet.Cells[13, dem + 4].Value = 0; //số lô hàng
                excelWorksheet.Cells[13, dem + 5].Value = listTruckSea.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[13, dem + 6].Value = listTruckSea.Sum(x => x.TotalGW); // trọng lượng

                //10. Số lượng xe chưa cân:
                var listTruckPickUpChuaCan = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(loadingArrivalDate: p_Date)).Where(x => !x.UnloadingArrivalDate.HasValue);
                excelWorksheet.Cells[15, dem + 3].Value = listTruckPickUpChuaCan.Count(); //số chuyến xe
                excelWorksheet.Cells[15, dem + 4].Value = 0; //số lô hàng
                excelWorksheet.Cells[15, dem + 5].Value = listTruckPickUpChuaCan.Sum(x => x.Pieces); //số kiện hàng
                excelWorksheet.Cells[15, dem + 6].Value = listTruckPickUpChuaCan.Sum(x => x.Weight); // trọng lượng

                //11. Lưu kho hàng air:
                var truckTransitQuery = (await _vehiclesRegistrationRepository.GetListTruckForCheckDocForReport1Async(transitStartDate: dateFrom, transitEndDate: dateTo));
                var lstDetailTruckNoTransit = truckTransitQuery.Where(x=>!x.VhclLoadingLeftDate.HasValue).ToList(); // xe đã tạo nhưng chưa transit
                var airInventory = (await _groupPrcvHistoryRepository.GetListInventoryBalanceAsync()).Queryable.Where(x => x.PltSeaAirType.ToUpper().Contains("AIR"));
                excelWorksheet.Cells[16, dem + 3].Value = lstDetailTruckNoTransit.Count(); //số chuyến xe
                excelWorksheet.Cells[16, dem + 4].Value = airInventory.Where(x=>x.MawbId > 0).Count() + lstDetailTruckNoTransit.Sum(x =>x.TotalMawb); //số lô hàng
                excelWorksheet.Cells[16, dem + 5].Value = airInventory.Sum(x => x.ReceivedPieces) + lstDetailTruckNoTransit.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[16, dem + 6].Value = airInventory.Sum(x => x.ReceivedWeight) + lstDetailTruckNoTransit.Sum(x => x.TotalGW); // trọng lượng

                //12. Lưu kho hàng sea:
                var seaInventory = (await _groupPrcvHistoryRepository.GetListInventoryBalanceAsync()).Queryable.Where(x=>x.PltSeaAirType.ToUpper().Contains("SEA"));
                excelWorksheet.Cells[17, dem + 3].Value = 0; //số chuyến xe
                excelWorksheet.Cells[17, dem + 4].Value = seaInventory.Where(x => x.MawbId > 0).Count(); //số lô hàng
                excelWorksheet.Cells[17, dem + 5].Value = seaInventory.Sum(x => x.ReceivedPieces); //số kiện hàng
                excelWorksheet.Cells[17, dem + 6].Value = seaInventory.Sum(x => x.ReceivedWeight); // trọng lượng

                //14. Xuất hàng đi NBA:
                //var truckTransit = (await _vehiclesRegistrationRepository.GetListTruckForCheckDocAsync(startDate: startOfDate, endDate: endOfDate)).ToList();
                var truckTransit = ObjectMapper.Map<List<ListCheckDoc>, List<ListCheckDocDto>>(
                                            truckTransitQuery.AsQueryable().PageBy(0, 200).ToList());
                foreach (var item in truckTransit)
                {
                    if (long.TryParse(item.UnloadingWarehouse, out long id))
                    {
                        var data = await _ertsRemoteTransitShedRepository.FindAsync(id);
                        item.UnloadingWarehouse = data is null ? string.Empty : data.ErtsShedDescription;
                    }
                }
                //var truckTransit = (await _checkDocumentsAppService.GetListTruckForCheckDocAsync(new GetListTruckForCheckDocInput
                //{
                //    TransitDate = transitDateString,
                //    SkipCount = 0,
                //    MaxResultCount = 200
                //})).Items.ToList(); 
                excelWorksheet.Cells[19, dem + 3].Value = truckTransit.Count(); //số chuyến xe
                excelWorksheet.Cells[19, dem + 4].Value = truckTransit.Sum(x => x.TotalMawb); //số lô hàng
                excelWorksheet.Cells[19, dem + 5].Value = truckTransit.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[19, dem + 6].Value = truckTransit.Sum(x => x.TotalGW); // trọng lượng
                
                //15. Xuất hàng đi ACSV:
                var truckTransitACSV = truckTransit.Where(x=>x.UnloadingWarehouse == "ACSV");
                excelWorksheet.Cells[20, dem + 3].Value = truckTransitACSV.Count(); //số chuyến xe
                excelWorksheet.Cells[20, dem + 4].Value = truckTransitACSV.Sum(x => x.TotalMawb); //số lô hàng
                excelWorksheet.Cells[20, dem + 5].Value = truckTransitACSV.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[20, dem + 6].Value = truckTransitACSV.Sum(x => x.TotalGW); // trọng lượng

                //16. Xuất hàng đi ALSC:
                var truckTransitALSC = truckTransit.Where(x => x.UnloadingWarehouse == "ALSC");
                excelWorksheet.Cells[21, dem + 3].Value = truckTransitALSC.Count(); //số chuyến xe
                excelWorksheet.Cells[21, dem + 4].Value = truckTransitALSC.Sum(x => x.TotalMawb); //số lô hàng
                excelWorksheet.Cells[21, dem + 5].Value = truckTransitALSC.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[21, dem + 6].Value = truckTransitALSC.Sum(x => x.TotalGW); // trọng lượng

                //17. Xuất hàng đi NCTS:
                var truckTransitNCTS = truckTransit.Where(x => x.UnloadingWarehouse == "NCTS");
                excelWorksheet.Cells[22, dem + 3].Value = truckTransitNCTS.Count(); //số chuyến xe
                excelWorksheet.Cells[22, dem + 4].Value = truckTransitNCTS.Sum(x => x.TotalMawb); //số lô hàng
                excelWorksheet.Cells[22, dem + 5].Value = truckTransitNCTS.Sum(x => x.TotalPcs); //số kiện hàng
                excelWorksheet.Cells[22, dem + 6].Value = truckTransitNCTS.Sum(x => x.TotalGW); // trọng lượng

                // Update Style Bảng
                excelWorksheet.Cells["C6:F22"].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells["C6:F22"].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells["C6:F22"].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells["C6:F22"].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                excelWorksheet.Cells["C6:F22"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;// dồn sang phải
            }
            catch (Exception)
            {
                throw;
            }
        }

        #region DownloadBBNhanHangDO2 Result Class

        /// <summary>
        /// Class chứa kết quả của DownloadBBNhanHangDO2Async
        /// </summary>
        public class FileResult
        {
            /// <summary>
            /// Số biên bản từ CountTruckUnloadingForReport
            /// </summary>
            public string FileName { get; set; }
            public string FileExtension { get; set; } = ".xlsx";

            /// <summary>
            /// Nội dung file Excel/PDF
            /// </summary>
            public IRemoteStreamContent FileContent { get; set; }
        }

        #endregion

        #region CountTruckUnloadingForReport2 - Improved Version
        public async Task<FileResult> DownloadBBNhanHangDO2Async(DownloadBBNhanVaVanChuyenHangInput input)
        {
            string soBienBan = string.Empty;

            // Read file template
            var stream = await BlobContainer.GetAsync(input.FileId);
            using (ExcelPackage excelPackage = new(stream))
            {
                // Get sheet
                ExcelWorksheet excelWorksheetDsTong = excelPackage.Workbook.Worksheets[0];

                var truckUnloading = (await _vehiclesRegistrationRepositoryMaster.GetVehiclesUnLoadingExportListAsync(vehicRegId: input.p_TruckId, type: "EXPORT", truckType: "PICK UP")).ToList()[0];
                var dateUnload = truckUnloading.UnloadingArrivalDate.Value;
                soBienBan = await CountTruckUnloadingForReport(truckUnloading.Id);



                await FillDataToBBNhanVaVanChuyenHang(excelWorksheetDsTong, truckUnloading, soBienBan);
                excelPackage.Save();
                stream = excelPackage.Stream;
                if (input.p_ExportType == "PDF")
                {
                    // convert to PDF
                    Workbook workbook = new Workbook();
                    workbook.LoadFromStream(stream);
                    // 2. Chuyển đổi sang PDF
                    workbook.SaveToStream(stream, Spire.Xls.FileFormat.PDF);
                }
            }

            return new FileResult
            {
                FileName = soBienBan,
                FileContent = new RemoteStreamContent(stream)
            };
        }
        /// <summary>
        /// Phiên bản cải tiến của CountTruckUnloadingForReport
        /// Tính toán số lượng xe trong tháng có thời gian unloading trước xe truyền vào
        /// </summary>
        /// <param name="truckId">Id của xe truyền vào</param>
        /// <returns>Chuỗi kết quả theo format: {Count}-{Month}-{VehicleRegNo}-{UnloadingDate}</returns>
        /// <exception cref="ArgumentException">Khi truckId không hợp lệ</exception>
        /// <exception cref="UserFriendlyException">Khi không tìm thấy xe hoặc xe chưa có ngày unloading</exception>
        public async Task<string> CountTruckUnloadingForReport2(long truckId)
        {
            // Constants
            const string EXPORT_TYPE = "EXPORT";
            const string PICKUP_TRUCK_TYPE = "PICK UP";
            const string SEV_VENDOR = "SEV";

            // Validation đầu vào
            if (truckId <= 0)
                throw new ArgumentException("ID xe không hợp lệ", nameof(truckId));

            try
            {
                // Lấy thông tin xe cần tính toán
                var truckUnloadList = await _vehiclesRegistrationRepositoryMaster
                    .GetVehiclesUnLoadingExportListAsync(
                        vehicRegId: truckId,
                        type: EXPORT_TYPE,
                        truckType: PICKUP_TRUCK_TYPE);

                if (!truckUnloadList.Any())
                    throw new UserFriendlyException($"Không tìm thấy xe với ID {truckId}");

                var truckUnload = truckUnloadList.First();

                if (!truckUnload.UnloadingArrivalDate.HasValue)
                    throw new UserFriendlyException($"Xe {truckId} chưa có thời gian unloading");

                var dateUnload = truckUnload.UnloadingArrivalDate.Value;
                var vendor = truckUnload.Factory;

                // Xác định loại vendor
                bool isSevVendor = !string.IsNullOrWhiteSpace(vendor) && vendor.Contains(SEV_VENDOR);

                // Tính ngày đầu tháng
                DateTime firstDayOfMonth = new DateTime(dateUnload.Year, dateUnload.Month, 1, 0, 0, 0);

                // Lấy danh sách xe unloading trong khoảng thời gian
                var trucksInPeriod = await _vehiclesRegistrationRepositoryMaster
                    .GetQueryVehiclesUnLoadingExportForReport(
                        firstDayOfMonth,
                        dateUnload,
                        EXPORT_TYPE,
                        PICKUP_TRUCK_TYPE);

                // Đếm số xe theo loại vendor
                int count = 0;
                if (isSevVendor)
                {
                    count = trucksInPeriod
                        .Count(x => x.UnloadingArrivalDate != null &&
                                   !string.IsNullOrWhiteSpace(x.Factory) &&
                                   x.Factory.Contains(SEV_VENDOR));
                }
                else
                {
                    count = trucksInPeriod
                        .Count(x => x.UnloadingArrivalDate != null &&
                                   (string.IsNullOrWhiteSpace(x.Factory) ||
                                    !x.Factory.Contains(SEV_VENDOR)));
                }

                // Tạo chuỗi kết quả
                string result = $"{count}-{dateUnload:MM}-{truckUnload.VehicRegNo}-{dateUnload:dd.MM.yyyy}";
                return result;
            }
            catch (ArgumentException)
            {
                throw;
            }
            catch (UserFriendlyException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException($"Lỗi khi tính toán số lượng xe unloading cho truck ID {truckId}: {ex.Message}");
            }
        }

        #endregion

    }
}
