﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="Pages"><Directory Name="InboundModule"><Directory Name="AWBDetail"><File Name="AddNewAWBModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.AddNewAWBModal.js</ResourcePath></File><File Name="AwbSearch.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.AwbSearch.js</ResourcePath></File><Directory Name="CD"><File Name="addCDModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.CD.addCDModal.js</ResourcePath></File></Directory><File Name="ConfirmDeliveryModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.ConfirmDeliveryModal.js</ResourcePath></File><File Name="EditAWBModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.EditAWBModal.js</ResourcePath></File><File Name="EditAWBNumberModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.EditAWBNumberModal.js</ResourcePath></File><File Name="EditHAWBNumberModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.EditHAWBNumberModal.js</ResourcePath></File><File Name="EditNotesModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.EditNotesModal.js</ResourcePath></File><File Name="FlightDetailsModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.FlightDetailsModal.js</ResourcePath></File><Directory Name="Group"><File Name="EditGroupLocationModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.Group.EditGroupLocationModal.js</ResourcePath></File><File Name="EditGroupModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.Group.EditGroupModal.js</ResourcePath></File><File Name="addGroupClassModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.Group.addGroupClassModal.js</ResourcePath></File><File Name="addGroupModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.Group.addGroupModal.js</ResourcePath></File></Directory><File Name="HawbDetails.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.HawbDetails.js</ResourcePath></File><Directory Name="MawbCharge"><File Name="DLVBillDetailModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.DLVBillDetailModal.js</ResourcePath></File><File Name="DLVBillModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.DLVBillModal.js</ResourcePath></File><Directory Name="MultiDeliveryPrint"><File Name="index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.MultiDeliveryPrint.index.js</ResourcePath></File></Directory><File Name="chargeInformationModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.chargeInformationModal.js</ResourcePath></File><File Name="chargeManualModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.chargeManualModal.js</ResourcePath></File><File Name="listInvoiceRunModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.listInvoiceRunModal.js</ResourcePath></File><File Name="mawbChargeModal.css"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.mawbChargeModal.css</ResourcePath></File><File Name="mawbChargeModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.mawbChargeModal.js</ResourcePath></File><File Name="reasonDLVModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.reasonDLVModal.js</ResourcePath></File><File Name="reasonModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.MawbCharge.reasonModal.js</ResourcePath></File></Directory><Directory Name="Monitoring"><File Name="AwbMonitoring.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.Monitoring.AwbMonitoring.js</ResourcePath></File></Directory><Directory Name="OperationCenter"><File Name="OperationCenter.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.OperationCenter.OperationCenter.js</ResourcePath></File></Directory><File Name="QuickAddIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.QuickAddIrregularModal.js</ResourcePath></File><File Name="QuickEditIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.QuickEditIrregularModal.js</ResourcePath></File><File Name="ShowImageModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.ShowImageModal.js</ResourcePath></File><File Name="UpdateCustomsClearanceModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.UpdateCustomsClearanceModal.js</ResourcePath></File><File Name="addBookFlightModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.addBookFlightModal.js</ResourcePath></File><File Name="addIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.addIrregularModal.js</ResourcePath></File><File Name="customsDeclarationModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.customsDeclarationModal.js</ResourcePath></File><File Name="editBookFlightModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.editBookFlightModal.js</ResourcePath></File><File Name="editIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.editIrregularModal.js</ResourcePath></File><File Name="historyModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.historyModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.index.js</ResourcePath></File><File Name="listDeliveryHistoryModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.listDeliveryHistoryModal.js</ResourcePath></File><File Name="mawbDetails.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.mawbDetails.js</ResourcePath></File><File Name="messageModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.AWBDetail.messageModal.js</ResourcePath></File></Directory><Directory Name="CargoTerminal"><Directory Name="OlaDeclarations"><File Name="OlaDeclarations.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.OlaDeclarations.js</ResourcePath></File><File Name="UploadOlaModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.UploadOlaModal.js</ResourcePath></File><File Name="addHawbToTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.addHawbToTruckModal.js</ResourcePath></File><File Name="assignTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.assignTruckModal.js</ResourcePath></File><File Name="editEcusInfoModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.editEcusInfoModal.js</ResourcePath></File><File Name="hawbOnTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.hawbOnTruckModal.js</ResourcePath></File><File Name="sendInfoToEcusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.OlaDeclarations.sendInfoToEcusModal.js</ResourcePath></File></Directory><Directory Name="PickupPlans"><File Name="CreatePlan.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.CreatePlan.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.Index.js</ResourcePath></File><File Name="Index1.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.Index1.js</ResourcePath></File><File Name="PickupPlan.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.PickupPlan.js</ResourcePath></File><File Name="PickupPlanDetails.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.PickupPlanDetails.js</ResourcePath></File><File Name="UpdateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.PickupPlans.UpdateStatusModal.js</ResourcePath></File></Directory><Directory Name="TruckLoading"><File Name="AddGroupAwbToTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.AddGroupAwbToTruckModal.js</ResourcePath></File><File Name="ChangeTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.ChangeTruckModal.js</ResourcePath></File><File Name="GroupListByMawbSerialModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.GroupListByMawbSerialModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.GroupListModal.js</ResourcePath></File><File Name="GroupListUnloadModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.GroupListUnloadModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.detailTruck.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.truckLoading.js</ResourcePath></File><File Name="updateHawbStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.updateHawbStatusModal.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoading.updateStatusModal.js</ResourcePath></File></Directory><Directory Name="TruckLoadingV1"><File Name="ChangeTruckNumberModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.ChangeTruckNumberModal.js</ResourcePath></File><File Name="GroupListByMawbSerialModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.GroupListByMawbSerialModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.GroupListModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.addTruckLoading.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.CargoTerminal.TruckLoadingV1.truckLoading.js</ResourcePath></File></Directory></Directory><Directory Name="Custom"><Directory Name="EcusDownloads"><File Name="EcusDownload.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Custom.EcusDownloads.EcusDownload.js</ResourcePath></File></Directory></Directory><Directory Name="DeliveryShipments"><File Name="DeliveryShipment.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.DeliveryShipments.DeliveryShipment.js</ResourcePath></File><File Name="DeliveryShipmentDetails.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.DeliveryShipments.DeliveryShipmentDetails.js</ResourcePath></File><File Name="SelectShipments.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.DeliveryShipments.SelectShipments.js</ResourcePath></File></Directory><Directory Name="Factory"><Directory Name="PodManagement"><File Name="index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Factory.PodManagement.index.js</ResourcePath></File><File Name="updatePodClcModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Factory.PodManagement.updatePodClcModal.js</ResourcePath></File><File Name="updatePodModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Factory.PodManagement.updatePodModal.js</ResourcePath></File></Directory></Directory><Directory Name="FlightMonitor"><File Name="addFlightModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.FlightMonitor.addFlightModal.js</ResourcePath></File><File Name="flightDetailModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.FlightMonitor.flightDetailModal.js</ResourcePath></File><File Name="flightMonitor.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.FlightMonitor.flightMonitor.js</ResourcePath></File><File Name="historyModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.FlightMonitor.historyModal.js</ResourcePath></File><File Name="manifestList.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.FlightMonitor.manifestList.js</ResourcePath></File></Directory><Directory Name="ShipmentLocations"><File Name="ShipmentLocation.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.ShipmentLocations.ShipmentLocation.js</ResourcePath></File></Directory><Directory Name="TruckAndConsol"><Directory Name="Consol"><File Name="Index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.Index.js</ResourcePath></File><File Name="UpdateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.UpdateStatusModal.js</ResourcePath></File><File Name="addAwbToPodTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.addAwbToPodTruckModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.detailTruck.js</ResourcePath></File><File Name="podManageModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Consol.podManageModal.js</ResourcePath></File></Directory><Directory Name="Truck"><File Name="AddAwb.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Truck.AddAwb.js</ResourcePath></File><File Name="DetailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Truck.DetailTruck.js</ResourcePath></File><File Name="Index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Truck.Index.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.TruckAndConsol.Truck.addTruckLoading.js</ResourcePath></File></Directory></Directory><Directory Name="Warehouse"><File Name="AddNewTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.AddNewTruckModal.js</ResourcePath></File><Directory Name="CheckLoadUnloads"><File Name="CheckLoadUnload.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CheckLoadUnloads.CheckLoadUnload.js</ResourcePath></File><File Name="updatePiecesModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CheckLoadUnloads.updatePiecesModal.js</ResourcePath></File></Directory><Directory Name="ClassUpdate"><File Name="ImportExcelModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ClassUpdate.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ClassUpdate.Index.js</ResourcePath></File><File Name="Upload.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ClassUpdate.Upload.js</ResourcePath></File></Directory><Directory Name="CustomsDeclarations"><File Name="ImportExcelModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CustomsDeclarations.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CustomsDeclarations.Index.js</ResourcePath></File><File Name="UploadCD.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CustomsDeclarations.UploadCD.js</ResourcePath></File><File Name="updateCDModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.CustomsDeclarations.updateCDModal.js</ResourcePath></File></Directory><Directory Name="DeliveryPlans"><Directory Name="ALSB"><File Name="CreatePlan.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.ALSB.CreatePlan.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.ALSB.ImportExcelModal.js</ResourcePath></File></Directory><File Name="CreatePlan.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.CreatePlan.js</ResourcePath></File><File Name="DeliveryPlan.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.DeliveryPlan.js</ResourcePath></File><File Name="DeliveryPlanDetails.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.DeliveryPlanDetails.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.ImportExcelModal.js</ResourcePath></File><File Name="UpdateDeliveryInfoModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.DeliveryPlans.UpdateDeliveryInfoModal.js</ResourcePath></File></Directory><Directory Name="ShipmentOrder"><File Name="AwbListModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.AwbListModal.js</ResourcePath></File><File Name="CreateShipmentOrder.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.CreateShipmentOrder.js</ResourcePath></File><File Name="DetailShipmentOrder.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.DetailShipmentOrder.js</ResourcePath></File><File Name="EditShipmentOrder.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.EditShipmentOrder.js</ResourcePath></File><File Name="ListShipmentOrder.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.ListShipmentOrder.js</ResourcePath></File><File Name="ShipmentOrder.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.ShipmentOrder.js</ResourcePath></File><File Name="UpdateDeliveryInfoModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.ShipmentOrder.UpdateDeliveryInfoModal.js</ResourcePath></File></Directory><Directory Name="TruckLoadingDLV"><File Name="AddGroupAwbToTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.AddGroupAwbToTruckModal.js</ResourcePath></File><File Name="GroupListByLagiIdModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.GroupListByLagiIdModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.GroupListModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.addTruckLoading.js</ResourcePath></File><File Name="assignPodModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.assignPodModal.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.detailTruck.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.truckLoading.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLV.updateStatusModal.js</ResourcePath></File></Directory><Directory Name="TruckLoadingDLVV1_bak"><File Name="EditTruckStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.EditTruckStatusModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.GroupListModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.detailTruck.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV1_bak.truckLoading.js</ResourcePath></File></Directory><Directory Name="TruckLoadingDLVV2"><File Name="addAwbToPodTruckModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.addAwbToPodTruckModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.detailTruck.js</ResourcePath></File><File Name="podManageClcModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.podManageClcModal.js</ResourcePath></File><File Name="podManageModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.podManageModal.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckLoadingDLVV2.truckLoading.js</ResourcePath></File></Directory><Directory Name="TruckUnLoading"><File Name="detailTruckUnloading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoading.detailTruckUnloading.js</ResourcePath></File><File Name="editTruckUnLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoading.editTruckUnLoading.js</ResourcePath></File><File Name="truckUnLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoading.truckUnLoading.js</ResourcePath></File><File Name="updateLocationModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoading.updateLocationModal.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoading.updateStatusModal.js</ResourcePath></File></Directory><Directory Name="TruckUnLoadingV1"><File Name="ChangeTruckNumberModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.ChangeTruckNumberModal.js</ResourcePath></File><Directory Name="Group"><File Name="shipmentModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.Group.shipmentModal.js</ResourcePath></File></Directory><File Name="GroupListUnloadModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.GroupListUnloadModal.js</ResourcePath></File><File Name="editTruckUnLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.editTruckUnLoading.js</ResourcePath></File><File Name="truckUnLoading.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.truckUnLoading.js</ResourcePath></File><File Name="updateLocationModal.js"><ResourcePath>InboundModule.Web.Pages.InboundModule.Warehouse.TruckUnLoadingV1.updateLocationModal.js</ResourcePath></File></Directory></Directory></Directory><Directory Name="Mobile"><Directory Name="DeliveryAWB"><File Name="DeliveryAWB.js"><ResourcePath>InboundModule.Web.Pages.Mobile.DeliveryAWB.DeliveryAWB.js</ResourcePath></File><File Name="TruckListPOD.js"><ResourcePath>InboundModule.Web.Pages.Mobile.DeliveryAWB.TruckListPOD.js</ResourcePath></File><File Name="TruckPodDetail.js"><ResourcePath>InboundModule.Web.Pages.Mobile.DeliveryAWB.TruckPodDetail.js</ResourcePath></File><File Name="UpdateArrivedFactoryModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.DeliveryAWB.UpdateArrivedFactoryModal.js</ResourcePath></File><File Name="UpdateDeliveryModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.DeliveryAWB.UpdateDeliveryModal.js</ResourcePath></File></Directory><Directory Name="TruckLoading"><File Name="AddNewTruckModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.AddNewTruckModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.GroupListModal.js</ResourcePath></File><File Name="IrregularityModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.IrregularityModal.js</ResourcePath></File><File Name="addIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.addIrregularModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.addTruckLoading.js</ResourcePath></File><File Name="closeModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.closeModal.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.detailTruck.js</ResourcePath></File><File Name="editIrregularModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.editIrregularModal.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.truckLoading.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>InboundModule.Web.Pages.Mobile.TruckLoading.updateStatusModal.js</ResourcePath></File></Directory></Directory><Directory Name="Shared"><File Name="AssestmentsTruck.js"><ResourcePath>InboundModule.Web.Pages.Shared.AssestmentsTruck.js</ResourcePath></File></Directory></Directory></FileSystem></Manifest>