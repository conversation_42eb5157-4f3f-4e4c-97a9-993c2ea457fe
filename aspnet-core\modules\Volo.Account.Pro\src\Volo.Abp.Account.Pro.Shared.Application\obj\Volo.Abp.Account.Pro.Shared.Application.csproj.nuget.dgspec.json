{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application\\Volo.Abp.Account.Pro.Shared.Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj", "projectName": "Volo.Abp.Account.Pro.Shared.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.Pro.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ldap": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application\\Volo.Abp.Account.Pro.Shared.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application\\Volo.Abp.Account.Pro.Shared.Application.csproj", "projectName": "Volo.Abp.Account.Pro.Shared.Application", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application\\Volo.Abp.Account.Pro.Shared.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.Account.Pro\\src\\Volo.Abp.Account.Pro.Shared.Application.Contracts\\Volo.Abp.Account.Pro.Shared.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Text.Encodings.Web": {"target": "Package", "version": "[5.0.*, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Emailing": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Identity.Pro.Application": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.UI.Navigation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}}}