#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\src\AOMS.Web\Themes\Lepton\Layouts\Application\AfterScripts.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "524da365c6609e4c2fa72aa29d4d7597b8525021"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Themes_Lepton_Layouts_Application_AfterScripts), @"mvc.1.0.view", @"/Themes/Lepton/Layouts/Application/AfterScripts.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 1 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\src\AOMS.Web\Themes\_ViewImports.cshtml"
using System.Globalization;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"524da365c6609e4c2fa72aa29d4d7597b8525021", @"/Themes/Lepton/Layouts/Application/AfterScripts.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"41d51eae9e844421817080a898274b7f478f62ad", @"/Themes/_ViewImports.cshtml")]
    public class Themes_Lepton_Layouts_Application_AfterScripts : global::Microsoft.AspNetCore.Mvc.Razor.RazorPage<dynamic>
    {
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
            WriteLiteral(@"<script type=""text/javascript"" src=""/js/dataTables.buttons.min.js""></script>
<script type=""text/javascript"" src=""/js/jszip.min.js""></script>
<script type=""text/javascript"" src=""/js/pdfmake.min.js""></script>
<script type=""text/javascript"" src=""/js/vfs_fonts.js""></script>
<script type=""text/javascript"" src=""/js/buttons.html5.min.js""></script>
<script type=""text/javascript"" src=""/js/buttons.print.min.js""></script>
<link rel=""stylesheet"" type=""text/css"" href=""/css/buttons.dataTables.min.css"" />");
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<dynamic> Html { get; private set; }
    }
}
#pragma warning restore 1591
