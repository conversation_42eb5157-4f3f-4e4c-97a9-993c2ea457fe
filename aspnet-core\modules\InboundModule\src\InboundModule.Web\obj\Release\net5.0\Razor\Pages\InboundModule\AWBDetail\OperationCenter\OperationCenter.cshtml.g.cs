#pragma checksum "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "654a819abad7c7889e6751c22ca1f4edda5b153b"
// <auto-generated/>
#pragma warning disable 1591
[assembly: global::Microsoft.AspNetCore.Razor.Hosting.RazorCompiledItemAttribute(typeof(AspNetCore.Pages_InboundModule_AWBDetail_OperationCenter_OperationCenter), @"mvc.1.0.razor-page", @"/Pages/InboundModule/AWBDetail/OperationCenter/OperationCenter.cshtml")]
namespace AspNetCore
{
    #line hidden
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Rendering;
    using Microsoft.AspNetCore.Mvc.ViewFeatures;
#nullable restore
#line 2 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using Microsoft.AspNetCore.Authorization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 3 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using Volo.Abp.AspNetCore.Mvc.UI.Layout;

#line default
#line hidden
#nullable disable
#nullable restore
#line 4 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using InboundModule.Permissions;

#line default
#line hidden
#nullable disable
#nullable restore
#line 5 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using InboundModule.Web.Pages.InboundModule.AWBDetail;

#line default
#line hidden
#nullable disable
#nullable restore
#line 6 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using InboundModule.Web.Menus;

#line default
#line hidden
#nullable disable
#nullable restore
#line 7 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using Microsoft.AspNetCore.Mvc.Localization;

#line default
#line hidden
#nullable disable
#nullable restore
#line 8 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
using InboundModule.Localization;

#line default
#line hidden
#nullable disable
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"654a819abad7c7889e6751c22ca1f4edda5b153b", @"/Pages/InboundModule/AWBDetail/OperationCenter/OperationCenter.cshtml")]
    [global::Microsoft.AspNetCore.Razor.Hosting.RazorSourceChecksumAttribute(@"SHA1", @"229e689a848d94625f3fd15edcb2d99cbdb5996f", @"/Pages/_ViewImports.cshtml")]
    public class Pages_InboundModule_AWBDetail_OperationCenter_OperationCenter : global::Microsoft.AspNetCore.Mvc.RazorPages.Page
    {
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_0 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/Pages/InboundModule/AWBDetail/OperationCenter/OperationCenter.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_1 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery-migrate-3.0.0.min.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_2 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery-ui.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_3 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/popper.min.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_4 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/BsMultiSelect.bs4.min.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_5 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery.inputmask.bundle.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_6 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/js/jquery.datetimepicker.full.js", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_7 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("src", "/css/BsMultiSelect.min.css", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_8 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_9 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "0", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_10 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("value", "1", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_11 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("btn btn-primary btn-block"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_12 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnSearchUncomplete"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_13 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("type", new global::Microsoft.AspNetCore.Html.HtmlString("submit"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_14 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("icon", "search", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_15 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("text", "Search", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_16 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("mt-1"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_17 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("form-group"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_18 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("margintop10"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_19 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("mt-3"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_20 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("title", "Uncompleted", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_21 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("btnSearchComplete"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_22 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("dtComplete"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_23 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("title", "Completed", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_24 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("class", new global::Microsoft.AspNetCore.Html.HtmlString("p-2"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_25 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("title", "Summary", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_26 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("name", "Summary", global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_27 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("id", new global::Microsoft.AspNetCore.Html.HtmlString("SearchForm"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        private static readonly global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute __tagHelperAttribute_28 = new global::Microsoft.AspNetCore.Razor.TagHelpers.TagHelperAttribute("autocomplete", new global::Microsoft.AspNetCore.Html.HtmlString("off"), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
        #line hidden
        #pragma warning disable 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperExecutionContext __tagHelperExecutionContext;
        #pragma warning restore 0649
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner __tagHelperRunner = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperRunner();
        #pragma warning disable 0169
        private string __tagHelperStringValueBuffer;
        #pragma warning restore 0169
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __backed__tagHelperScopeManager = null;
        private global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager __tagHelperScopeManager
        {
            get
            {
                if (__backed__tagHelperScopeManager == null)
                {
                    __backed__tagHelperScopeManager = new global::Microsoft.AspNetCore.Razor.Runtime.TagHelpers.TagHelperScopeManager(StartTagHelperWritingScope, EndTagHelperWritingScope);
                }
                return __backed__tagHelperScopeManager;
            }
        }
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpStyleTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpStyleTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardBodyTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabsTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper;
        private global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper;
        private global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableTagHelper __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper;
        #pragma warning disable 1998
        public async override global::System.Threading.Tasks.Task ExecuteAsync()
        {
#nullable restore
#line 13 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
  
    PageLayout.Content.Title = L["Menu:OperationCenter"].Value;
    PageLayout.Content.BreadCrumb.Add(L["Menu:InboundModule"].Value);
    PageLayout.Content.MenuItemName = InboundModuleMenus.IbOperationCenter;
    ViewBag.Title = L["Menu:OperationCenter"].Value;
    ViewBag.MetaDescription = "";

#line default
#line hidden
#nullable disable
            WriteLiteral("\r\n");
            DefineSection("scripts", async() => {
                WriteLiteral("\r\n");
                WriteLiteral("    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b17737", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_0.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_0);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b18978", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_1.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_1);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b20213", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_2.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_2);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b21448", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_3.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_3);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b22683", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_4.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_4);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b23918", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_5.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_5);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-script", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b25153", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpScriptTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpScriptTagHelper.Src = (string)__tagHelperAttribute_6.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_6);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n    <link href=\"/css/jquery.datetimepicker.min.css\" rel=\"Stylesheet\" type=\"text/css\" />\r\n    <link href=\"/css/jquery-ui.css\" rel=\"Stylesheet\" type=\"text/css\" />\r\n    <link href=\"/css/custom.css\" rel=\"Stylesheet\" type=\"text/css\" />\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-style", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.SelfClosing, "654a819abad7c7889e6751c22ca1f4edda5b153b26644", async() => {
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bundling.TagHelpers.AbpStyleTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpStyleTagHelper);
                __Volo_Abp_AspNetCore_Mvc_UI_Bundling_TagHelpers_AbpStyleTagHelper.Src = (string)__tagHelperAttribute_7.Value;
                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_7);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            WriteLiteral(@"<style>
    #dtComplete th,
    td {
        white-space: nowrap;
    }

    .nav-tabs .nav-item .nav-link.active {
        color: #fff;
        background: #6f42c1;
    }

    .box-item {
        width: 100%;
        float: left;
        position: relative;
    }

    .sol-current-selection {
        display: none;
    }

    .tab-content {
        padding: 0.1rem 1.75rem;
    }


    .btn {
        padding: 5px 10px;
    }

    .jump {
        text-decoration: underline;
        color: blue;
        font-weight: 600;
        margin: 0px 15px;
        cursor: pointer
    }
</style>

");
            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b28559", async() => {
                WriteLiteral("\r\n    ");
                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-card-body", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b28826", async() => {
                    WriteLiteral("\r\n        <input type=\"hidden\" id=\"Id\">\r\n        <input type=\"hidden\" id=\"hiddenAgentCompleteParameters\">\r\n        <input type=\"hidden\" id=\"hiddenAgentUncompleteParameters\">\r\n        <input type=\"hidden\" id=\"hiddenStatusParameters\">\r\n        ");
                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("form", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b29366", async() => {
                        WriteLiteral("\r\n            ");
                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tabs", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b29653", async() => {
                            WriteLiteral("\r\n                ");
                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tab", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b29956", async() => {
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b30270", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b30596", async() => {
                                        WriteLiteral("\r\n                            ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b30937", async() => {
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b31287", async() => {
                                                WriteLiteral("\r\n                                    <label for=\"mawbHawb\">MAWB/HAWB</label>\r\n                                    <select id=\"mawbHawb\" class=\"form-control select2\">\r\n                                    </select>\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 87 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size-md", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b33741", async() => {
                                                WriteLiteral("\r\n                                    <label for=\"FlightDate\">Flight Date</label>\r\n                                    <input placeholder=\"dd/mm/yyyy - dd/mm/yyyy\" id=\"FlightDate\" class=\"form-control\" />\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 92 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size-md", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b36185", async() => {
                                                WriteLiteral("\r\n                                    <label for=\"CDStatus\">CD Status</label>\r\n                                    <select id=\"CDStatus\" class=\"form-control\">\r\n                                        ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b36722", async() => {
                                                    WriteLiteral(" Tất cả ");
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b38500", async() => {
                                                    WriteLiteral(" Chưa thông quan ");
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_9.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_9);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b40287", async() => {
                                                    WriteLiteral(" Đã thông quan ");
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_10.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_10);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                    </select>\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 96 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b43976", async() => {
                                                WriteLiteral("\r\n                                    <label for=\"ddlCargoTerminal\">Cargo Terminal</label>\r\n                                    <select id=\"ddlCargoTerminal\" class=\"form-control\">\r\n                                        ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b44534", async() => {
                                                    WriteLiteral(" Tất cả ");
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n");
#nullable restore
#line 108 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                          
                                            foreach (var item in Model.WareHouseShedSelectList)
                                            {
                                                if (item.Text == "Cargo terminal")
                                                {

#line default
#line hidden
#nullable disable
                                                WriteLiteral("                                                    ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b46978", async() => {
#nullable restore
#line 113 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                                                  Write(item.Text);

#line default
#line hidden
#nullable disable
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                BeginWriteTagHelperAttribute();
#nullable restore
#line 113 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                      WriteLiteral(item.Value);

#line default
#line hidden
#nullable disable
                                                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = __tagHelperStringValueBuffer;
                                                __tagHelperExecutionContext.AddTagHelperAttribute("value", __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                BeginWriteTagHelperAttribute();
                                                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                __tagHelperExecutionContext.AddHtmlAttribute("selected", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.Minimized);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n");
#nullable restore
#line 114 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                }
                                                else
                                                {

#line default
#line hidden
#nullable disable
                                                WriteLiteral("                                                    ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b50618", async() => {
#nullable restore
#line 117 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                                         Write(item.Text);

#line default
#line hidden
#nullable disable
                                                }
                                                );
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                BeginWriteTagHelperAttribute();
#nullable restore
#line 117 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                      WriteLiteral(item.Value);

#line default
#line hidden
#nullable disable
                                                __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = __tagHelperStringValueBuffer;
                                                __tagHelperExecutionContext.AddTagHelperAttribute("value", __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n");
#nullable restore
#line 118 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                }

                                            }
                                        

#line default
#line hidden
#nullable disable
                                                WriteLiteral("                                    </select>\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 104 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n");
                                            WriteLiteral("                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b55770", async() => {
                                                WriteLiteral(@"
                                    <label for=""agentOptionUncomplete"">Forwarder/Agent</label>
                                    <select id=""agentOptionUncomplete"" multiple=""multiple"" style=""display:none;"">
                                    </select>
                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 133 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._3;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b58258", async() => {
                                                WriteLiteral("\r\n                                    <label></label>\r\n                                    ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b58678", async() => {
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
#nullable restore
#line 140 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                                                __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_12);
                                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Icon = (string)__tagHelperAttribute_14.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_14);
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_15.Value;
                                                __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_15);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 138 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._1;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                            ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 85 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b67899", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b68225", async() => {
                                        WriteLiteral(@"
                            <b> Jumpto: </b><a class=""jump"" href=""#dtUnCompleteArrived"" id=""ArrivalNotice"">Arrival Notice</a>
                            <a class=""jump"" href=""#dtUnCompleteDelivery"" id=""InNBA"">In NBA</a>
                            <a class=""jump"" href=""#dtUnCompleteLoadingOnTruck"" id=""InTransit"">In Transit</a>
                            <a class=""jump"" href=""#dtUnCompleteDelivering"" id=""InCLC""></a>
                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 147 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                \r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b71888", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b72214", async() => {
                                        WriteLiteral("\r\n                            <div style=\"overflow-x:auto;\">\r\n                                <table class=\"table table-bordered table-striped dataTable\" id=\"dtUnCompleteArrived\" style=\"display:none;\">\r\n                                    ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("thead", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b72776", async() => {
                                            WriteLiteral("\r\n                                        ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b73132", async() => {
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b73497", async() => {
                                                    WriteLiteral("Action");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b75087", async() => {
                                                    WriteLiteral("Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b76677", async() => {
                                                    WriteLiteral("MAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b78265", async() => {
                                                    WriteLiteral("HAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b79853", async() => {
                                                    WriteLiteral("Pcs");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b81440", async() => {
                                                    WriteLiteral("GW");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b83026", async() => {
                                                    WriteLiteral("Flight");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b84616", async() => {
                                                    WriteLiteral("Flight Date");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b86211", async() => {
                                                    WriteLiteral("RQ Delivery");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b87806", async() => {
                                                    WriteLiteral("CD Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b89399", async() => {
                                                    WriteLiteral("Agent");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b90988", async() => {
                                                    WriteLiteral("Cargoterminal");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b92585", async() => {
                                                    WriteLiteral("Remark");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                                    <tbody>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 156 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b99531", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b99857", async() => {
                                        WriteLiteral("\r\n                            <div style=\"overflow-x:auto;\">\r\n                                <table class=\"table table-bordered table-striped dataTable\" id=\"dtUnCompleteDelivery\" style=\"display:none;\">\r\n                                    ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("thead", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b100420", async() => {
                                            WriteLiteral("\r\n                                        ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b100777", async() => {
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b101143", async() => {
                                                    WriteLiteral("Action");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b102734", async() => {
                                                    WriteLiteral("Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b104325", async() => {
                                                    WriteLiteral("MAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b105914", async() => {
                                                    WriteLiteral("HAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b107503", async() => {
                                                    WriteLiteral("Pcs");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b109091", async() => {
                                                    WriteLiteral("GW");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b110678", async() => {
                                                    WriteLiteral("Flight");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b112269", async() => {
                                                    WriteLiteral("Flight Date");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b113865", async() => {
                                                    WriteLiteral("RQ Delivery");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b115461", async() => {
                                                    WriteLiteral("CD Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b117055", async() => {
                                                    WriteLiteral("Agent");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b118645", async() => {
                                                    WriteLiteral("Cargoterminal");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b120243", async() => {
                                                    WriteLiteral("Remark");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                                    <tbody>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 183 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b127190", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b127517", async() => {
                                        WriteLiteral("\r\n                            <div style=\"overflow-x:auto;\">\r\n                                <table class=\"table table-bordered table-striped dataTable\" id=\"dtUnCompleteLoadingOnTruck\" style=\"display:none;\">\r\n                                    ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("thead", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b128087", async() => {
                                            WriteLiteral("\r\n                                        ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b128444", async() => {
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b128810", async() => {
                                                    WriteLiteral("Action");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b130401", async() => {
                                                    WriteLiteral("Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b131992", async() => {
                                                    WriteLiteral("MAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b133581", async() => {
                                                    WriteLiteral("HAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b135170", async() => {
                                                    WriteLiteral("Pcs");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b136758", async() => {
                                                    WriteLiteral("GW");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b138345", async() => {
                                                    WriteLiteral("Flight");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b139936", async() => {
                                                    WriteLiteral("Flight Date");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b141532", async() => {
                                                    WriteLiteral("RQ Delivery");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b143128", async() => {
                                                    WriteLiteral("CD Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b144722", async() => {
                                                    WriteLiteral("Truck No");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b146315", async() => {
                                                    WriteLiteral("Transit time");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b147912", async() => {
                                                    WriteLiteral("Agent");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b149502", async() => {
                                                    WriteLiteral("Cargoterminal");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b151100", async() => {
                                                    WriteLiteral("Remark");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                                    <tbody>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 210 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b158051", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b158378", async() => {
                                        WriteLiteral("\r\n                            <div style=\"overflow-x:auto;\">\r\n                                <table class=\"table table-bordered table-striped dataTable\" id=\"dtUnCompleteDelivering\" style=\"display:none;\">\r\n                                    ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("thead", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b158944", async() => {
                                            WriteLiteral("\r\n                                        ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("tr", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b159301", async() => {
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b159667", async() => {
                                                    WriteLiteral("Action");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b161258", async() => {
                                                    WriteLiteral("Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b162849", async() => {
                                                    WriteLiteral("MAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b164438", async() => {
                                                    WriteLiteral("HAWB");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b166027", async() => {
                                                    WriteLiteral("Pcs");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b167615", async() => {
                                                    WriteLiteral("GW");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b169202", async() => {
                                                    WriteLiteral("Flight");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b170793", async() => {
                                                    WriteLiteral("Flight Date");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b172389", async() => {
                                                    WriteLiteral("RQ Delivery");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b173985", async() => {
                                                    WriteLiteral("CD Status");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b175579", async() => {
                                                    WriteLiteral("Truck No");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b177172", async() => {
                                                    WriteLiteral("Transit time");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b178769", async() => {
                                                    WriteLiteral("Agent");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b180359", async() => {
                                                    WriteLiteral("Cargoterminal");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                            ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("th", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b181957", async() => {
                                                    WriteLiteral("Remark");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeadScopeTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeadScopeTagHelper);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                        ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableStyleTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableStyleTagHelper);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableHeaderTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableHeaderTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                                    <tbody>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 240 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_19);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n\r\n                ");
                            }
                            );
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper>();
                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper);
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Title = (string)__tagHelperAttribute_20.Value;
                            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_20);
                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                            {
                                await __tagHelperExecutionContext.SetOutputContentAsync();
                            }
                            Write(__tagHelperExecutionContext.Output);
                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                            WriteLiteral("\r\n                ");
                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tab", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b190107", async() => {
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b190422", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b190749", async() => {
                                        WriteLiteral("\r\n                            ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b191091", async() => {
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b191442", async() => {
                                                WriteLiteral("\r\n                                    ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b191808", async() => {
                                                    WriteLiteral("\r\n                                        ");
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b192183", async() => {
                                                        WriteLiteral(@"
                                            <label for=""mawbHawb1"">MAWB/HAWB</label>
                                            <select id=""mawbHawb1"" class=""form-control select2"" style=""width:100% !important"">
                                            </select>
                                        ");
                                                    }
                                                    );
                                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 276 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._3;

#line default
#line hidden
#nullable disable
                                                    __tagHelperExecutionContext.AddTagHelperAttribute("size-md", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                    {
                                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                                    }
                                                    Write(__tagHelperExecutionContext.Output);
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                    WriteLiteral("\r\n                                        ");
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b194826", async() => {
                                                        WriteLiteral(@"
                                            <label for=""FlightDate1"">Flight Date</label>
                                            <input placeholder=""dd/mm/yyyy - dd/mm/yyyy"" id=""FlightDate1"" class=""form-control"" />
                                        ");
                                                    }
                                                    );
                                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 281 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                                    __tagHelperExecutionContext.AddTagHelperAttribute("size-md", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                    {
                                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                                    }
                                                    Write(__tagHelperExecutionContext.Output);
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                    WriteLiteral("\r\n\r\n                                        ");
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b197425", async() => {
                                                        WriteLiteral("\r\n                                            <label for=\"ddlCargoTerminal1\">Cargo Terminal</label>\r\n                                            <select id=\"ddlCargoTerminal1\" class=\"form-control\">\r\n                                                ");
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b198026", async() => {
                                                            WriteLiteral(" Tất cả ");
                                                        }
                                                        );
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                        __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = (string)__tagHelperAttribute_8.Value;
                                                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_8);
                                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                        {
                                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                                        }
                                                        Write(__tagHelperExecutionContext.Output);
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                        WriteLiteral("\r\n");
#nullable restore
#line 290 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                  
                                                    foreach (var item in Model.WareHouseShedSelectList)
                                                    {
                                                        if (item.Text == "Cargo terminal")
                                                        {

#line default
#line hidden
#nullable disable
                                                        WriteLiteral("                                                            ");
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b200655", async() => {
#nullable restore
#line 295 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                                                          Write(item.Text);

#line default
#line hidden
#nullable disable
                                                        }
                                                        );
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                        __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                        BeginWriteTagHelperAttribute();
#nullable restore
#line 295 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                              WriteLiteral(item.Value);

#line default
#line hidden
#nullable disable
                                                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = __tagHelperStringValueBuffer;
                                                        __tagHelperExecutionContext.AddTagHelperAttribute("value", __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                        BeginWriteTagHelperAttribute();
                                                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                        __tagHelperExecutionContext.AddHtmlAttribute("selected", Html.Raw(__tagHelperStringValueBuffer), global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.Minimized);
                                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                        {
                                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                                        }
                                                        Write(__tagHelperExecutionContext.Output);
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                        WriteLiteral("\r\n");
#nullable restore
#line 296 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                        }
                                                        else
                                                        {

#line default
#line hidden
#nullable disable
                                                        WriteLiteral("                                                            ");
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("option", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b204512", async() => {
#nullable restore
#line 299 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                                                 Write(item.Text);

#line default
#line hidden
#nullable disable
                                                        }
                                                        );
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper>();
                                                        __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper);
                                                        BeginWriteTagHelperAttribute();
#nullable restore
#line 299 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                              WriteLiteral(item.Value);

#line default
#line hidden
#nullable disable
                                                        __tagHelperStringValueBuffer = EndWriteTagHelperAttribute();
                                                        __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value = __tagHelperStringValueBuffer;
                                                        __tagHelperExecutionContext.AddTagHelperAttribute("value", __Microsoft_AspNetCore_Mvc_TagHelpers_OptionTagHelper.Value, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                        {
                                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                                        }
                                                        Write(__tagHelperExecutionContext.Output);
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                        WriteLiteral("\r\n");
#nullable restore
#line 300 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
                                                        }

                                                    }
                                                

#line default
#line hidden
#nullable disable
                                                        WriteLiteral("                                            </select>\r\n                                        ");
                                                    }
                                                    );
                                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 286 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                    {
                                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                                    }
                                                    Write(__tagHelperExecutionContext.Output);
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                    WriteLiteral("\r\n                                        ");
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b209914", async() => {
                                                        WriteLiteral(@"
                                            <label for=""ddlStatus4"">Forwarder/Agent</label>
                                            <select class=""form-control"" id=""agentOptionComplete"" multiple=""multiple"" style=""display:none;"">
                                            </select>
                                        ");
                                                    }
                                                    );
                                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 306 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._3;

#line default
#line hidden
#nullable disable
                                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                    {
                                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                                    }
                                                    Write(__tagHelperExecutionContext.Output);
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                    WriteLiteral("\r\n                                        ");
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b212573", async() => {
                                                        WriteLiteral("\r\n                                            <label></label>\r\n                                            ");
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-button", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b213026", async() => {
                                                        }
                                                        );
                                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonTagHelper>();
                                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper);
#nullable restore
#line 313 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Button.AbpButtonType.Primary;

#line default
#line hidden
#nullable disable
                                                        __tagHelperExecutionContext.AddTagHelperAttribute("button-type", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.ButtonType, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_11);
                                                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_21);
                                                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_13);
                                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Icon = (string)__tagHelperAttribute_14.Value;
                                                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_14);
                                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Button_AbpButtonTagHelper.Text = (string)__tagHelperAttribute_15.Value;
                                                        __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_15);
                                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                        {
                                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                                        }
                                                        Write(__tagHelperExecutionContext.Output);
                                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                        WriteLiteral("\r\n                                        ");
                                                    }
                                                    );
                                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 311 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._2;

#line default
#line hidden
#nullable disable
                                                    __tagHelperExecutionContext.AddTagHelperAttribute("size-md", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.SizeMd, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_16);
                                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                    {
                                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                                    }
                                                    Write(__tagHelperExecutionContext.Output);
                                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                    WriteLiteral("\r\n                                    ");
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_17);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 274 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                            ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n\r\n                            ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b222888", async() => {
                                            WriteLiteral("\r\n                                ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b223239", async() => {
                                                WriteLiteral("\r\n                                    ");
                                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-table", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b223605", async() => {
                                                }
                                                );
                                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableTagHelper>();
                                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper);
#nullable restore
#line 321 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper.BorderStyle = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Table.AbpTableBorderStyle.Bordered;

#line default
#line hidden
#nullable disable
                                                __tagHelperExecutionContext.AddTagHelperAttribute("border-style", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper.BorderStyle, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_22);
#nullable restore
#line 321 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper.StripedRows = true;

#line default
#line hidden
#nullable disable
                                                __tagHelperExecutionContext.AddTagHelperAttribute("striped-rows", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Table_AbpTableTagHelper.StripedRows, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                                {
                                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                                }
                                                Write(__tagHelperExecutionContext.Output);
                                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                                WriteLiteral("\r\n                                ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 320 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                            ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 272 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._12;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n                ");
                            }
                            );
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper>();
                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper);
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Title = (string)__tagHelperAttribute_23.Value;
                            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_23);
                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                            {
                                await __tagHelperExecutionContext.SetOutputContentAsync();
                            }
                            Write(__tagHelperExecutionContext.Output);
                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                            WriteLiteral("\r\n                ");
                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-tab", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b233608", async() => {
                                WriteLiteral("\r\n                    ");
                                __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b233923", async() => {
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b234250", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">Plan</h5>\r\n                                <h1 class=\"text-center\" id=\"PlanCount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b234810", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b235165", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"PlanPCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 334 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b237565", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=\"PlanGW\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 338 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 329 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b242962", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">In NBA</h5>\r\n                                <h1 class=\"text-center\" id=\"InNBACount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b243525", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b243880", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"InNBAPCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 350 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b246281", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=\"InNBAGW\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 354 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 345 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b251679", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">Transit to ALSX</h5>\r\n                                <h1 class=\"text-center\" id=\"TransitCount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b252253", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b252608", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"TransitPCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 366 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b255011", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=\"TransitGW\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 370 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 361 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b260411", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">In ALSX</h5>\r\n                                <h1 class=\"text-center\" id=\"inAlsxCount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b260976", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b261331", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"inAlsxPCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 382 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b263733", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=inAlsxGW></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 386 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 377 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b269128", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">DLV Today</h5>\r\n                                <h1 class=\"text-center\" id=\"DLVToDayCount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b269697", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b270052", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"DLVToDayPCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 398 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b272456", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=\"DLVToDayGW\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 402 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 393 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n                        ");
                                    __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b277857", async() => {
                                        WriteLiteral("\r\n                            <div class=\"border\">\r\n                                <h5 class=\"text-center mt-2\">Overdue</h5>\r\n                                <h1 class=\"text-center\" id=\"OverdueCount\"></h1>\r\n                                ");
                                        __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-row", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b278423", async() => {
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b278778", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">Pcs</h5>\r\n                                        <h4 class=\"text-center\" id=\"OverduePCS\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 414 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                    ");
                                            __tagHelperExecutionContext = __tagHelperScopeManager.Begin("abp-column", global::Microsoft.AspNetCore.Razor.TagHelpers.TagMode.StartTagAndEndTag, "654a819abad7c7889e6751c22ca1f4edda5b153b281181", async() => {
                                                WriteLiteral("\r\n                                        <h5 class=\"text-center\">GW</h5>\r\n                                        <h4 class=\"text-center\" id=\"OverdueGW\"></h4>\r\n                                    ");
                                            }
                                            );
                                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 418 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._6;

#line default
#line hidden
#nullable disable
                                            __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                                            {
                                                await __tagHelperExecutionContext.SetOutputContentAsync();
                                            }
                                            Write(__tagHelperExecutionContext.Output);
                                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                            WriteLiteral("\r\n                                ");
                                        }
                                        );
                                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                                        {
                                            await __tagHelperExecutionContext.SetOutputContentAsync();
                                        }
                                        Write(__tagHelperExecutionContext.Output);
                                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                        WriteLiteral("\r\n                            </div>\r\n                        ");
                                    }
                                    );
                                    __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpColumnTagHelper>();
                                    __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper);
#nullable restore
#line 409 "C:\Users\<USER>\Documents\Source_Code\AOMS_GITLAB\aspnet-core\modules\InboundModule\src\InboundModule.Web\Pages\InboundModule\AWBDetail\OperationCenter\OperationCenter.cshtml"
__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size = global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.ColumnSize._4;

#line default
#line hidden
#nullable disable
                                    __tagHelperExecutionContext.AddTagHelperAttribute("size", __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpColumnTagHelper.Size, global::Microsoft.AspNetCore.Razor.TagHelpers.HtmlAttributeValueStyle.DoubleQuotes);
                                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_24);
                                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                                    {
                                        await __tagHelperExecutionContext.SetOutputContentAsync();
                                    }
                                    Write(__tagHelperExecutionContext.Output);
                                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                    WriteLiteral("\r\n\r\n                    ");
                                }
                                );
                                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Grid.AbpRowTagHelper>();
                                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Grid_AbpRowTagHelper);
                                __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                                if (!__tagHelperExecutionContext.Output.IsContentModified)
                                {
                                    await __tagHelperExecutionContext.SetOutputContentAsync();
                                }
                                Write(__tagHelperExecutionContext.Output);
                                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                                WriteLiteral("\r\n\r\n                ");
                            }
                            );
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabTagHelper>();
                            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper);
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Title = (string)__tagHelperAttribute_25.Value;
                            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_25);
                            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabTagHelper.Name = (string)__tagHelperAttribute_26.Value;
                            __tagHelperExecutionContext.AddTagHelperAttribute(__tagHelperAttribute_26);
                            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                            if (!__tagHelperExecutionContext.Output.IsContentModified)
                            {
                                await __tagHelperExecutionContext.SetOutputContentAsync();
                            }
                            Write(__tagHelperExecutionContext.Output);
                            __tagHelperExecutionContext = __tagHelperScopeManager.End();
                            WriteLiteral("\r\n            ");
                        }
                        );
                        __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Tab.AbpTabsTagHelper>();
                        __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Tab_AbpTabsTagHelper);
                        __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_18);
                        await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                        if (!__tagHelperExecutionContext.Output.IsContentModified)
                        {
                            await __tagHelperExecutionContext.SetOutputContentAsync();
                        }
                        Write(__tagHelperExecutionContext.Output);
                        __tagHelperExecutionContext = __tagHelperScopeManager.End();
                        WriteLiteral("\r\n        ");
                    }
                    );
                    __Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_FormTagHelper);
                    __Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper = CreateTagHelper<global::Microsoft.AspNetCore.Mvc.TagHelpers.RenderAtEndOfFormTagHelper>();
                    __tagHelperExecutionContext.Add(__Microsoft_AspNetCore_Mvc_TagHelpers_RenderAtEndOfFormTagHelper);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_27);
                    __tagHelperExecutionContext.AddHtmlAttribute(__tagHelperAttribute_28);
                    await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                    if (!__tagHelperExecutionContext.Output.IsContentModified)
                    {
                        await __tagHelperExecutionContext.SetOutputContentAsync();
                    }
                    Write(__tagHelperExecutionContext.Output);
                    __tagHelperExecutionContext = __tagHelperScopeManager.End();
                    WriteLiteral("\r\n    ");
                }
                );
                __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardBodyTagHelper>();
                __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardBodyTagHelper);
                await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
                if (!__tagHelperExecutionContext.Output.IsContentModified)
                {
                    await __tagHelperExecutionContext.SetOutputContentAsync();
                }
                Write(__tagHelperExecutionContext.Output);
                __tagHelperExecutionContext = __tagHelperScopeManager.End();
                WriteLiteral("\r\n");
            }
            );
            __Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper = CreateTagHelper<global::Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Card.AbpCardTagHelper>();
            __tagHelperExecutionContext.Add(__Volo_Abp_AspNetCore_Mvc_UI_Bootstrap_TagHelpers_Card_AbpCardTagHelper);
            await __tagHelperRunner.RunAsync(__tagHelperExecutionContext);
            if (!__tagHelperExecutionContext.Output.IsContentModified)
            {
                await __tagHelperExecutionContext.SetOutputContentAsync();
            }
            Write(__tagHelperExecutionContext.Output);
            __tagHelperExecutionContext = __tagHelperScopeManager.End();
            WriteLiteral("\r\n");
        }
        #pragma warning restore 1998
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IPageLayout PageLayout { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IAuthorizationService Authorization { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public IHtmlLocalizer<InboundModuleResource> L { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider ModelExpressionProvider { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IUrlHelper Url { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.IViewComponentHelper Component { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper Json { get; private set; }
        [global::Microsoft.AspNetCore.Mvc.Razor.Internal.RazorInjectAttribute]
        public global::Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper<InboundModule.Web.Pages.InboundModule.AWBDetail.OperationCenter.OperationCenter> Html { get; private set; }
        public global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<InboundModule.Web.Pages.InboundModule.AWBDetail.OperationCenter.OperationCenter> ViewData => (global::Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary<InboundModule.Web.Pages.InboundModule.AWBDetail.OperationCenter.OperationCenter>)PageContext?.ViewData;
        public InboundModule.Web.Pages.InboundModule.AWBDetail.OperationCenter.OperationCenter Model => ViewData.Model;
    }
}
#pragma warning restore 1591
