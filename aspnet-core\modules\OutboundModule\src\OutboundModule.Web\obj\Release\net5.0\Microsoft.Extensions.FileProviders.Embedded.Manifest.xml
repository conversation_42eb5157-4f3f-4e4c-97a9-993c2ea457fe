﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="Pages"><Directory Name="Mobile"><Directory Name="TruckPickup"><File Name="AddNewPOModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.AddNewPOModal.js</ResourcePath></File><File Name="AddNewTruckModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.AddNewTruckModal.js</ResourcePath></File><File Name="AddSealModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.AddSealModal.js</ResourcePath></File><File Name="EditPcsModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.EditPcsModal.js</ResourcePath></File><File Name="PoListModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.PoListModal.js</ResourcePath></File><File Name="ScanDoModal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.ScanDoModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.detailTruck.js</ResourcePath></File><File Name="truckPickup.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.truckPickup.js</ResourcePath></File><File Name="updateSeal.js"><ResourcePath>OutboundModule.Web.Pages.Mobile.TruckPickup.updateSeal.js</ResourcePath></File></Directory></Directory><Directory Name="OutboundModule"><Directory Name="AwbDetail"><File Name="AwbSearch.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.AwbSearch.js</ResourcePath></File><Directory Name="Hawb"><Directory Name="HsplHawbSplits"><File Name="createModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.HsplHawbSplits.createModal.js</ResourcePath></File></Directory><File Name="addHawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.addHawbModal.js</ResourcePath></File><File Name="buildUpModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.buildUpModal.js</ResourcePath></File><File Name="changeHawbToMawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.changeHawbToMawbModal.js</ResourcePath></File><File Name="editHawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.editHawbModal.js</ResourcePath></File><File Name="splitHawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.splitHawbModal.js</ResourcePath></File><File Name="updateHawbShipmentModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.Hawb.updateHawbShipmentModal.js</ResourcePath></File></Directory><Directory Name="MawbCharge"><File Name="chargeInformationModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.MawbCharge.chargeInformationModal.js</ResourcePath></File><File Name="chargeManualModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.MawbCharge.chargeManualModal.js</ResourcePath></File><File Name="listInvoiceRunModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.MawbCharge.listInvoiceRunModal.js</ResourcePath></File><File Name="mawbChargeModal.css"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.MawbCharge.mawbChargeModal.css</ResourcePath></File><File Name="mawbChargeModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.MawbCharge.mawbChargeModal.js</ResourcePath></File></Directory><File Name="ShowImageModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.ShowImageModal.js</ResourcePath></File><File Name="addBookFlightModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.addBookFlightModal.js</ResourcePath></File><File Name="addIrregularModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.addIrregularModal.js</ResourcePath></File><File Name="awbDetailStatusModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.awbDetailStatusModal.js</ResourcePath></File><File Name="editFlightModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.editFlightModal.js</ResourcePath></File><File Name="editIrregularModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.editIrregularModal.js</ResourcePath></File><File Name="hawbDetails.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.hawbDetails.js</ResourcePath></File><File Name="historyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.historyModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.index.js</ResourcePath></File><File Name="mawbDetails.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.mawbDetails.js</ResourcePath></File><File Name="messageModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.messageModal.js</ResourcePath></File><File Name="updateUcrCdModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.AwbDetail.updateUcrCdModal.js</ResourcePath></File></Directory><Directory Name="CargoTerminal"><File Name="updateStatus.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CargoTerminal.updateStatus.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CargoTerminal.updateStatusModal.js</ResourcePath></File></Directory><Directory Name="CelloIntegration"><Directory Name="CargoPickup"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.CargoPickup.Index.js</ResourcePath></File></Directory><Directory Name="GRGIReport"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRGIReport.Index.js</ResourcePath></File></Directory><Directory Name="GRPlan"><Directory Name="CelloGRPlan"><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.CelloGRPlan.ImportExcelModal.js</ResourcePath></File><File Name="NotifyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.CelloGRPlan.NotifyModal.js</ResourcePath></File></Directory><Directory Name="DoException"><File Name="addDoException.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.DoException.addDoException.js</ResourcePath></File><File Name="editDoException.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.DoException.editDoException.js</ResourcePath></File></Directory><Directory Name="Forecast"><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.Forecast.ImportExcelModal.js</ResourcePath></File><File Name="NotifyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.Forecast.NotifyModal.js</ResourcePath></File></Directory><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.GRPlan.Index.js</ResourcePath></File></Directory><Directory Name="SummaryGRPlan"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.CelloIntegration.SummaryGRPlan.Index.js</ResourcePath></File></Directory></Directory><Directory Name="ChsSync"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.ChsSync.Index.js</ResourcePath></File><File Name="addChsSync.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.ChsSync.addChsSync.js</ResourcePath></File><File Name="editChsSync.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.ChsSync.editChsSync.js</ResourcePath></File></Directory><Directory Name="Common"><File Name="NotifyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Common.NotifyModal.js</ResourcePath></File></Directory><Directory Name="Custom"><Directory Name="EcusDownloads"><File Name="EcusDownload.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Custom.EcusDownloads.EcusDownload.js</ResourcePath></File></Directory></Directory><Directory Name="Factory"><Directory Name="SOManage"><File Name="AssigneeModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.AssigneeModal.js</ResourcePath></File><File Name="CreateSO.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.CreateSO.js</ResourcePath></File><File Name="DenyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.DenyModal.js</ResourcePath></File><File Name="DetailSO.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.DetailSO.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.SOManage.Index.js</ResourcePath></File></Directory><Directory Name="TruckPickup"><File Name="DoPoListModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.DoPoListModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.addTruckLoading.js</ResourcePath></File><File Name="changeSealModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.changeSealModal.js</ResourcePath></File><File Name="changeVehicleRegNoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.changeVehicleRegNoModal.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.detailTruck.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckPickup.truckLoading.js</ResourcePath></File></Directory><Directory Name="TruckReturn"><File Name="addPalletToPodTruckModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckReturn.addPalletToPodTruckModal.js</ResourcePath></File><File Name="addTruckReturn.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckReturn.addTruckReturn.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckReturn.detailTruck.js</ResourcePath></File><File Name="index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckReturn.index.js</ResourcePath></File><File Name="podManageModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Factory.TruckReturn.podManageModal.js</ResourcePath></File></Directory></Directory><Directory Name="FlightMonitor"><File Name="addFlightModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.FlightMonitor.addFlightModal.js</ResourcePath></File><File Name="flightDetailModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.FlightMonitor.flightDetailModal.js</ResourcePath></File><File Name="flightMonitor.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.FlightMonitor.flightMonitor.js</ResourcePath></File><File Name="historyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.FlightMonitor.historyModal.js</ResourcePath></File><File Name="manifestList.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.FlightMonitor.manifestList.js</ResourcePath></File></Directory><Directory Name="OperationCenter"><File Name="OperationCenter.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.OperationCenter.OperationCenter.js</ResourcePath></File><File Name="OperationCenterALSB.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.OperationCenter.OperationCenterALSB.js</ResourcePath></File><File Name="OperationCenterCHS.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.OperationCenter.OperationCenterCHS.js</ResourcePath></File></Directory><Directory Name="PlanManage"><Directory Name="ForecastManage"><File Name="Create.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.ForecastManage.Create.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.ForecastManage.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.ForecastManage.Index.js</ResourcePath></File></Directory><Directory Name="GRManage"><File Name="Create.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.GRManage.Create.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.GRManage.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.GRManage.Index.js</ResourcePath></File></Directory><Directory Name="OngoingManage"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.OngoingManage.Index.js</ResourcePath></File></Directory><Directory Name="SOManage"><File Name="AssigneeModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.AssigneeModal.js</ResourcePath></File><File Name="CreateSO.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.CreateSO.js</ResourcePath></File><File Name="DenyModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.DenyModal.js</ResourcePath></File><File Name="DetailSO.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.DetailSO.js</ResourcePath></File><File Name="ImportExcelModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.ImportExcelModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.PlanManage.SOManage.Index.js</ResourcePath></File></Directory></Directory><Directory Name="ShipmentLocations"><File Name="ShipmentLocation.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.ShipmentLocations.ShipmentLocation.js</ResourcePath></File></Directory><Directory Name="Timekeeping"><File Name="addTimekeeping.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Timekeeping.addTimekeeping.js</ResourcePath></File><File Name="timekeeping.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Timekeeping.timekeeping.js</ResourcePath></File></Directory><Directory Name="Tools"><File Name="EditGroupModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Tools.EditGroupModal.js</ResourcePath></File><File Name="Tools.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Tools.Tools.js</ResourcePath></File></Directory><Directory Name="TotalVolume"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.TotalVolume.Index.js</ResourcePath></File><File Name="addTotalVolume.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.TotalVolume.addTotalVolume.js</ResourcePath></File><File Name="editTotalVolume.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.TotalVolume.editTotalVolume.js</ResourcePath></File></Directory><Directory Name="Warehouse"><File Name="AddNewTruckModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.AddNewTruckModal.js</ResourcePath></File><Directory Name="CheckCD"><File Name="checkCD.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckCD.checkCD.js</ResourcePath></File></Directory><Directory Name="CheckDocuments"><File Name="AddCDModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckDocuments.AddCDModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckDocuments.Index.js</ResourcePath></File><File Name="checkCD.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckDocuments.checkCD.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckDocuments.detailTruck.js</ResourcePath></File><File Name="messageModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.CheckDocuments.messageModal.js</ResourcePath></File></Directory><Directory Name="Inventory"><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Inventory.Index.js</ResourcePath></File><File Name="UpdateLocationModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Inventory.UpdateLocationModal.js</ResourcePath></File></Directory><Directory Name="Manifest"><Directory Name="ALSB"><File Name="CreateManifest.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ALSB.CreateManifest.js</ResourcePath></File><File Name="EditManifiestModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ALSB.EditManifiestModal.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ALSB.Index.js</ResourcePath></File><File Name="ManifestDetail.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ALSB.ManifestDetail.js</ResourcePath></File><File Name="PrintSLIConfirmModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ALSB.PrintSLIConfirmModal.js</ResourcePath></File></Directory><File Name="CompleteManyMawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.CompleteManyMawbModal.js</ResourcePath></File><File Name="CreateManifest.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.CreateManifest.js</ResourcePath></File><File Name="Index.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.Index.js</ResourcePath></File><File Name="ManifestDetail.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.Manifest.ManifestDetail.js</ResourcePath></File></Directory><Directory Name="ReceiveShipment"><File Name="EditGroupModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.EditGroupModal.js</ResourcePath></File><File Name="ManageDIMModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ManageDIMModal.js</ResourcePath></File><File Name="MixDoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.MixDoModal.js</ResourcePath></File><File Name="ReceivePlanDetails.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceivePlanDetails.js</ResourcePath></File><File Name="ReceiveShipment.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveShipment.js</ResourcePath></File><File Name="ReceiveWeightScaling.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScaling.js</ResourcePath></File><File Name="ReceiveWeightScalingV1.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ReceiveWeightScalingV1.js</ResourcePath></File><File Name="ScaleLocationAndWarehouseModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ScaleLocationAndWarehouseModal.js</ResourcePath></File><File Name="ScaleLocationAndWarehouseV1Modal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.ScaleLocationAndWarehouseV1Modal.js</ResourcePath></File><File Name="SetPalletIdToDoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.SetPalletIdToDoModal.js</ResourcePath></File><File Name="UpdateLocationModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.UpdateLocationModal.js</ResourcePath></File><File Name="VehicleListModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceiveShipment.VehicleListModal.js</ResourcePath></File></Directory><Directory Name="ReceivingPlan"><File Name="AWBListModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.AWBListModal.js</ResourcePath></File><File Name="AddNewAWBModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.AddNewAWBModal.js</ResourcePath></File><File Name="ApproveReceivingPlanModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.ApproveReceivingPlanModal.js</ResourcePath></File><File Name="CreatePlan.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.CreatePlan.js</ResourcePath></File><File Name="ImportExcel.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.ImportExcel.js</ResourcePath></File><File Name="ReceivePlanDetails.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.ReceivePlanDetails.js</ResourcePath></File><File Name="ReceivingPlan.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.ReceivingPlan.js</ResourcePath></File><File Name="SelectAWBModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.SelectAWBModal.js</ResourcePath></File><File Name="UpdateStatusModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.ReceivingPlan.UpdateStatusModal.js</ResourcePath></File></Directory><Directory Name="SearchDo"><File Name="MixDoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.MixDoModal.js</ResourcePath></File><File Name="detailPalletModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.detailPalletModal.js</ResourcePath></File><File Name="editPalletModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.editPalletModal.js</ResourcePath></File><File Name="searchDo.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.SearchDo.searchDo.js</ResourcePath></File></Directory><Directory Name="TruckLoading"><File Name="AddGroupAwbToTruckModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.AddGroupAwbToTruckModal.js</ResourcePath></File><File Name="GroupListByMawbSerialModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.GroupListByMawbSerialModal.js</ResourcePath></File><File Name="GroupListModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.GroupListModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.addTruckLoading.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.detailTruck.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.editTruckLoading.js</ResourcePath></File><File Name="loadAWBToTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.loadAWBToTruck.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.truckLoading.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoading.updateStatusModal.js</ResourcePath></File></Directory><Directory Name="TruckLoadingCheck"><File Name="checkDoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingCheck.checkDoModal.js</ResourcePath></File><File Name="checkMawbModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingCheck.checkMawbModal.js</ResourcePath></File><File Name="checkTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingCheck.checkTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingCheck.truckLoading.js</ResourcePath></File></Directory><Directory Name="TruckLoadingPlan"><File Name="ChangeTruckNumberModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.ChangeTruckNumberModal.js</ResourcePath></File><File Name="addPalletToTruckModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.addPalletToTruckModal.js</ResourcePath></File><File Name="addTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.addTruckLoading.js</ResourcePath></File><File Name="detailTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.detailTruckLoading.js</ResourcePath></File><File Name="editTruckCargoModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.editTruckCargoModal.js</ResourcePath></File><File Name="editTruckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.editTruckLoading.js</ResourcePath></File><File Name="truckLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckLoadingPlan.truckLoading.js</ResourcePath></File></Directory><Directory Name="TruckUnloadingPickup"><File Name="DoPoListModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckUnloadingPickup.DoPoListModal.js</ResourcePath></File><File Name="detailTruck.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckUnloadingPickup.detailTruck.js</ResourcePath></File><File Name="editTruckUnloading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckUnloadingPickup.editTruckUnloading.js</ResourcePath></File><File Name="truckUnLoading.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckUnloadingPickup.truckUnLoading.js</ResourcePath></File><File Name="updateStatusModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.TruckUnloadingPickup.updateStatusModal.js</ResourcePath></File></Directory><File Name="addTransitPlan.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.addTransitPlan.js</ResourcePath></File><File Name="editAWBModal.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.editAWBModal.js</ResourcePath></File><File Name="editTransitPlan.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.editTransitPlan.js</ResourcePath></File><File Name="transitPlan.js"><ResourcePath>OutboundModule.Web.Pages.OutboundModule.Warehouse.transitPlan.js</ResourcePath></File></Directory></Directory></Directory></FileSystem></Manifest>