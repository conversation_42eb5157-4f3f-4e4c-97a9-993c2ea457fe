{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Web\\SeaModule.Web.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\MasterDataModule.Application.Contracts.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\MasterDataModule.Application.Contracts.csproj", "projectName": "MasterDataModule.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\MasterDataModule.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\MasterDataModule.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\MasterDataModule.Domain.Shared.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\Volo.FileManagement.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\Volo.FileManagement.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[2.2.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Authorization": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\MasterDataModule.Domain.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\MasterDataModule.Domain.Shared.csproj", "projectName": "MasterDataModule.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\MasterDataModule.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\OutboundModule.Domain.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\OutboundModule.Domain.Shared.csproj", "projectName": "OutboundModule.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\OutboundModule.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\OutboundModule.Domain.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\OutboundModule.Domain.csproj", "projectName": "OutboundModule.Domain", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\OutboundModule.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\OutboundModule.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain.Shared\\OutboundModule.Domain.Shared.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"target": "Package", "version": "[2.2.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\SeaModule.Application.Contracts.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\SeaModule.Application.Contracts.csproj", "projectName": "SeaModule.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\SeaModule.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\OutboundModule.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\OutboundModule\\src\\OutboundModule.Domain\\OutboundModule.Domain.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\SeaModule.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\SeaModule.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Authorization": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\SeaModule.Domain.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\SeaModule.Domain.Shared.csproj", "projectName": "SeaModule.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\SeaModule.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\SeaModule.HttpApi.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\SeaModule.HttpApi.csproj", "projectName": "SeaModule.HttpApi", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\SeaModule.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\SeaModule.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Application.Contracts\\SeaModule.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Web\\SeaModule.Web.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Web\\SeaModule.Web.csproj", "projectName": "SeaModule.Web", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Web\\SeaModule.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.Web\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\MasterDataModule.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\MasterDataModule\\src\\MasterDataModule.Application.Contracts\\MasterDataModule.Application.Contracts.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\SeaModule.HttpApi.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\SeaModule\\src\\SeaModule.HttpApi\\SeaModule.HttpApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.AutoMapper": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["portable-net45+win8+wp8+wpa81", "net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj", "projectName": "ShareDataModule.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\ShareDataModule.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\ShareDataModule.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Authorization": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\ShareDataModule.Domain.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\ShareDataModule.Domain.Shared.csproj", "projectName": "ShareDataModule.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\ShareDataModule.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\Volo.FileManagement.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\Volo.FileManagement.Application.Contracts.csproj", "projectName": "Volo.FileManagement.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\Volo.FileManagement.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\ShareDataModule\\src\\ShareDataModule.Application.Contracts\\ShareDataModule.Application.Contracts.csproj"}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[4.5.3.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Authorization": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj", "projectName": "Volo.FileManagement.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[4.5.3.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Features": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}}}