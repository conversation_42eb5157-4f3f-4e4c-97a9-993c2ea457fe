{"version": 2, "dgSpecHash": "4j4YfCdKv60=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.ui\\5.0.1\\aspnetcore.healthchecks.ui.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.ui.client\\5.0.1\\aspnetcore.healthchecks.ui.client.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.ui.core\\5.0.1\\aspnetcore.healthchecks.ui.core.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.ui.inmemory.storage\\5.0.1\\aspnetcore.healthchecks.ui.inmemory.storage.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac\\6.1.0\\autofac.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extensions.dependencyinjection\\7.1.0\\autofac.extensions.dependencyinjection.7.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extras.dynamicproxy\\6.0.0\\autofac.extras.dynamicproxy.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\10.1.1\\automapper.10.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.8.1\\azure.core.1.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.blobs\\12.8.0\\azure.storage.blobs.12.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.common\\12.7.0\\azure.storage.common.12.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\4.4.1\\castle.core.4.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core.asyncinterceptor\\2.0.21-alpha\\castle.core.asyncinterceptor.2.0.21-alpha.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\epplus\\4.5.3.3\\epplus.4.5.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\firebaseadmin\\2.2.0\\firebaseadmin.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fractions\\4.0.1\\fractions.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freespire.xls\\14.2.0\\freespire.xls.14.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax\\3.2.0\\google.api.gax.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax.rest\\3.2.0\\google.api.gax.rest.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis\\1.49.0\\google.apis.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.auth\\1.49.0\\google.apis.auth.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.core\\1.49.0\\google.apis.core.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\htmlagilitypack\\1.11.67\\htmlagilitypack.1.11.67.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.8.26\\humanizer.core.2.8.26.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identitymodel\\5.2.0\\identitymodel.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4\\4.1.1\\identityserver4.4.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4.aspnetidentity\\4.1.1\\identityserver4.aspnetidentity.4.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identityserver4.storage\\4.1.1\\identityserver4.storage.4.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2020.3.0\\jetbrains.annotations.2020.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\kubernetesclient\\3.0.12\\kubernetesclient.3.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ldapfornet\\2.7.11\\ldapfornet.2.7.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnet.mvc\\5.2.7\\microsoft.aspnet.mvc.5.2.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnet.razor\\3.2.7\\microsoft.aspnet.razor.3.2.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnet.webpages\\3.2.7\\microsoft.aspnet.webpages.3.2.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.google\\5.0.17\\microsoft.aspnetcore.authentication.google.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\5.0.5\\microsoft.aspnetcore.authentication.jwtbearer.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.microsoftaccount\\5.0.17\\microsoft.aspnetcore.authentication.microsoftaccount.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\3.1.0\\microsoft.aspnetcore.authentication.openidconnect.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.twitter\\5.0.17\\microsoft.aspnetcore.authentication.twitter.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\5.0.5\\microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\5.0.17\\microsoft.aspnetcore.cryptography.internal.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\5.0.5\\microsoft.aspnetcore.cryptography.keyderivation.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\5.0.17\\microsoft.aspnetcore.dataprotection.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\5.0.17\\microsoft.aspnetcore.dataprotection.abstractions.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.stackexchangeredis\\5.0.17\\microsoft.aspnetcore.dataprotection.stackexchangeredis.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.2.2\\microsoft.aspnetcore.http.2.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\5.0.5\\microsoft.aspnetcore.jsonpatch.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\5.0.5\\microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\2.2.0\\microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\2.2.5\\microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.formatters.json\\2.2.0\\microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\5.0.5\\microsoft.aspnetcore.mvc.newtonsoftjson.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\5.0.5\\microsoft.aspnetcore.mvc.razor.extensions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\5.0.5\\microsoft.aspnetcore.mvc.razor.runtimecompilation.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.versioning\\5.0.0\\microsoft.aspnetcore.mvc.versioning.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\5.0.5\\microsoft.aspnetcore.razor.language.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.responsecaching.abstractions\\2.2.0\\microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\5.0.0\\microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.0.0\\microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\3.8.0\\microsoft.codeanalysis.common.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\3.8.0\\microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\5.0.5\\microsoft.codeanalysis.razor.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\5.0.1\\microsoft.data.sqlite.core.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dotnet.platformabstractions\\3.1.6\\microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\5.0.17\\microsoft.entityframeworkcore.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\5.0.17\\microsoft.entityframeworkcore.abstractions.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\5.0.17\\microsoft.entityframeworkcore.analyzers.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\5.0.17\\microsoft.entityframeworkcore.design.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.inmemory\\5.0.1\\microsoft.entityframeworkcore.inmemory.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\5.0.17\\microsoft.entityframeworkcore.relational.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational.design\\2.0.0-preview1-final\\microsoft.entityframeworkcore.relational.design.2.0.0-preview1-final.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite\\5.0.1\\microsoft.entityframeworkcore.sqlite.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.core\\5.0.1\\microsoft.entityframeworkcore.sqlite.core.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.design\\2.0.0-preview1-final\\microsoft.entityframeworkcore.sqlite.design.2.0.0-preview1-final.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.tools\\5.0.17\\microsoft.entityframeworkcore.tools.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\3.0.0\\microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\5.0.0\\microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\5.0.0\\microsoft.extensions.caching.memory.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.stackexchangeredis\\5.0.1\\microsoft.extensions.caching.stackexchangeredis.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\5.0.0\\microsoft.extensions.configuration.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\5.0.0\\microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\5.0.0\\microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\5.0.0\\microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\5.0.0\\microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\5.0.0\\microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\5.0.0\\microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\5.0.0\\microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\5.0.2\\microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\5.0.0\\microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\5.0.0\\microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks\\5.0.1\\microsoft.extensions.diagnostics.healthchecks.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks.abstractions\\5.0.1\\microsoft.extensions.diagnostics.healthchecks.abstractions.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\5.0.0\\microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\5.0.17\\microsoft.extensions.fileproviders.embedded.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\5.0.0\\microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\5.0.0\\microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\5.0.0\\microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\5.0.0\\microsoft.extensions.http.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\5.0.5\\microsoft.extensions.identity.core.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\5.0.5\\microsoft.extensions.localization.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\5.0.5\\microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\5.0.0\\microsoft.extensions.logging.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\5.0.0\\microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\5.0.0\\microsoft.extensions.options.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\5.0.0\\microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\5.0.0\\microsoft.extensions.primitives.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.7.1\\microsoft.identitymodel.jsonwebtokens.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.7.1\\microsoft.identitymodel.logging.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\6.7.1\\microsoft.identitymodel.protocols.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\6.7.1\\microsoft.identitymodel.protocols.openidconnect.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.7.1\\microsoft.identitymodel.tokens.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.2.3\\microsoft.openapi.1.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.rest.clientruntime\\2.3.10\\microsoft.rest.clientruntime.2.3.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.web.infrastructure\\1.0.0\\microsoft.web.infrastructure.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\5.0.0\\microsoft.win32.systemevents.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.0\\netstandard.library.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.0\\nito.asyncex.context.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.coordination\\5.1.0\\nito.asyncex.coordination.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.0\\nito.asyncex.tasks.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.collections.deque\\1.1.0\\nito.collections.deque.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.0\\nito.disposables.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npoi\\2.5.5\\npoi.2.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuglify\\1.13.8\\nuglify.1.13.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oracle.entityframeworkcore\\5.21.1\\oracle.entityframeworkcore.5.21.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oracle.manageddataaccess.core\\3.21.1\\oracle.manageddataaccess.core.3.21.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\owl.recaptcha\\0.4.0\\owl.recaptcha.0.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.0.17\\pipelines.sockets.unofficial.2.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly\\7.2.1\\polly.7.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\portable.bouncycastle\\1.8.9\\portable.bouncycastle.1.8.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\scriban\\3.6.0\\scriban.3.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\2.10.0\\serilog.2.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\4.0.0\\serilog.aspnetcore.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\4.1.2\\serilog.extensions.hosting.4.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\3.0.1\\serilog.extensions.logging.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\1.1.0\\serilog.formatting.compact.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\3.1.0\\serilog.settings.configuration.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.async\\1.4.0\\serilog.sinks.async.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\3.1.1\\serilog.sinks.console.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\4.1.0\\serilog.sinks.file.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.3.2\\sharpziplib.1.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\1.68.0\\skiasharp.1.68.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.0.4\\sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.0.4\\sqlitepclraw.core.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.0.4\\sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.dynamic_cdecl\\2.0.4\\sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.0.593\\stackexchange.redis.2.0.593.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\6.1.1\\swashbuckle.aspnetcore.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.1.1\\swashbuckle.aspnetcore.swagger.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.1.1\\swashbuckle.aspnetcore.swaggergen.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.1.1\\swashbuckle.aspnetcore.swaggerui.6.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.7.0\\system.codedom.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\system.collections.immutable.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\5.0.0\\system.configuration.configurationmanager.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oracleclient\\1.0.8\\system.data.oracleclient.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\5.0.1\\system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\4.7.0\\system.diagnostics.performancecounter.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\4.7.0\\system.directoryservices.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\4.7.0\\system.directoryservices.protocols.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\5.0.0\\system.drawing.common.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\5.0.0\\system.formats.asn1.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.7.1\\system.identitymodel.tokens.jwt.6.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\4.7.0\\system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\4.5.1\\system.io.pipelines.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.2.9\\system.linq.dynamic.core.1.2.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\4.7.0\\system.management.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http.winhttphandler\\4.4.0\\system.net.http.winhttphandler.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.servicemodel\\4.4.4\\system.private.servicemodel.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.dispatchproxy\\4.4.0\\system.reflection.dispatchproxy.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\5.0.0\\system.reflection.metadata.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.7.0\\system.reflection.typeextensions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.7.1\\system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\5.0.0\\system.security.cryptography.pkcs.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\5.0.0\\system.security.cryptography.protecteddata.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.2\\system.security.cryptography.x509certificates.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\5.0.0\\system.security.cryptography.xml.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\5.0.0\\system.security.permissions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.duplex\\4.4.4\\system.servicemodel.duplex.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\4.4.4\\system.servicemodel.http.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\4.4.4\\system.servicemodel.nettcp.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\4.4.4\\system.servicemodel.primitives.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.security\\4.4.4\\system.servicemodel.security.4.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.7.0\\system.text.encoding.codepages.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\5.0.1\\system.text.encodings.web.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\4.5.0\\system.threading.channels.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.0.1\\system.threading.timer.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\5.0.0\\system.windows.extensions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xpath\\4.3.0\\system.xml.xpath.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xpath.xmldocument\\4.3.0\\system.xml.xpath.xmldocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\3.4.0\\timezoneconverter.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.apiversioning.abstractions\\4.3.0\\volo.abp.apiversioning.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore\\4.3.0\\volo.abp.aspnetcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.authentication.jwtbearer\\4.3.0\\volo.abp.aspnetcore.authentication.jwtbearer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.multitenancy\\4.3.0\\volo.abp.aspnetcore.multitenancy.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc\\4.3.0\\volo.abp.aspnetcore.mvc.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.contracts\\4.3.0\\volo.abp.aspnetcore.mvc.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui\\4.3.0\\volo.abp.aspnetcore.mvc.ui.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bootstrap\\4.3.0\\volo.abp.aspnetcore.mvc.ui.bootstrap.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bundling\\4.3.0\\volo.abp.aspnetcore.mvc.ui.bundling.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bundling.abstractions\\4.3.0\\volo.abp.aspnetcore.mvc.ui.bundling.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.multitenancy\\4.3.0\\volo.abp.aspnetcore.mvc.ui.multitenancy.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.packages\\4.3.0\\volo.abp.aspnetcore.mvc.ui.packages.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.theme.commercial\\4.3.0\\volo.abp.aspnetcore.mvc.ui.theme.commercial.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.theme.shared\\4.3.0\\volo.abp.aspnetcore.mvc.ui.theme.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.widgets\\4.3.0\\volo.abp.aspnetcore.mvc.ui.widgets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.serilog\\4.3.0\\volo.abp.aspnetcore.serilog.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.signalr\\4.3.0\\volo.abp.aspnetcore.signalr.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\4.3.0\\volo.abp.auditing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.application\\4.3.0\\volo.abp.auditlogging.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.application.contracts\\4.3.0\\volo.abp.auditlogging.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain\\4.3.0\\volo.abp.auditlogging.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.domain.shared\\4.3.0\\volo.abp.auditlogging.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.entityframeworkcore\\4.3.0\\volo.abp.auditlogging.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.httpapi\\4.3.0\\volo.abp.auditlogging.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditlogging.web\\4.3.0\\volo.abp.auditlogging.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\4.3.0\\volo.abp.authorization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\4.3.0\\volo.abp.authorization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.autofac\\4.3.0\\volo.abp.autofac.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\4.3.0\\volo.abp.automapper.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs\\4.3.0\\volo.abp.backgroundjobs.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.abstractions\\4.3.0\\volo.abp.backgroundjobs.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain\\4.3.0\\volo.abp.backgroundjobs.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.domain.shared\\4.3.0\\volo.abp.backgroundjobs.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundjobs.entityframeworkcore\\4.3.0\\volo.abp.backgroundjobs.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\4.3.0\\volo.abp.backgroundworkers.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring\\4.3.0\\volo.abp.blobstoring.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring.azure\\4.3.0\\volo.abp.blobstoring.azure.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring.database.domain\\4.3.0\\volo.abp.blobstoring.database.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring.database.domain.shared\\4.3.0\\volo.abp.blobstoring.database.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring.database.entityframeworkcore\\4.3.0\\volo.abp.blobstoring.database.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring.filesystem\\4.3.0\\volo.abp.blobstoring.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\4.3.0\\volo.abp.caching.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching.stackexchangeredis\\4.3.0\\volo.abp.caching.stackexchangeredis.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.castle.core\\4.3.0\\volo.abp.castle.core.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.commercial.core\\4.3.0\\volo.abp.commercial.core.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.commercial.suitetemplates\\4.3.0\\volo.abp.commercial.suitetemplates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\4.3.0\\volo.abp.core.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\4.3.0\\volo.abp.data.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application\\4.3.0\\volo.abp.ddd.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application.contracts\\4.3.0\\volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\4.3.0\\volo.abp.ddd.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.emailing\\4.3.0\\volo.abp.emailing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.entityframeworkcore\\4.3.0\\volo.abp.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.entityframeworkcore.oracle\\4.3.0\\volo.abp.entityframeworkcore.oracle.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\4.3.0\\volo.abp.eventbus.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\4.3.0\\volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\4.3.0\\volo.abp.exceptionhandling.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.application\\4.3.0\\volo.abp.featuremanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.application.contracts\\4.3.0\\volo.abp.featuremanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain\\4.3.0\\volo.abp.featuremanagement.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.domain.shared\\4.3.0\\volo.abp.featuremanagement.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.entityframeworkcore\\4.3.0\\volo.abp.featuremanagement.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.httpapi\\4.3.0\\volo.abp.featuremanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.featuremanagement.web\\4.3.0\\volo.abp.featuremanagement.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\4.3.0\\volo.abp.features.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.globalfeatures\\4.3.0\\volo.abp.globalfeatures.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\4.3.0\\volo.abp.guids.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http\\4.3.0\\volo.abp.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.abstractions\\4.3.0\\volo.abp.http.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.aspnetcore\\4.3.0\\volo.abp.identity.aspnetcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain\\4.3.0\\volo.abp.identity.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\4.3.0\\volo.abp.identity.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.entityframeworkcore\\4.3.0\\volo.abp.identity.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.application\\4.3.0\\volo.abp.identity.pro.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.application.contracts\\4.3.0\\volo.abp.identity.pro.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.domain\\4.3.0\\volo.abp.identity.pro.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.domain.shared\\4.3.0\\volo.abp.identity.pro.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.entityframeworkcore\\4.3.0\\volo.abp.identity.pro.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.httpapi\\4.3.0\\volo.abp.identity.pro.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.pro.web\\4.3.0\\volo.abp.identity.pro.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.application\\4.3.0\\volo.abp.identityserver.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.application.contracts\\4.3.0\\volo.abp.identityserver.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain\\4.3.0\\volo.abp.identityserver.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.domain.shared\\4.3.0\\volo.abp.identityserver.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.entityframeworkcore\\4.3.0\\volo.abp.identityserver.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.httpapi\\4.3.0\\volo.abp.identityserver.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identityserver.web\\4.3.0\\volo.abp.identityserver.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\4.3.0\\volo.abp.json.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.application\\4.3.0\\volo.abp.languagemanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.application.contracts\\4.3.0\\volo.abp.languagemanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.domain\\4.3.0\\volo.abp.languagemanagement.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.domain.shared\\4.3.0\\volo.abp.languagemanagement.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.entityframeworkcore\\4.3.0\\volo.abp.languagemanagement.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.httpapi\\4.3.0\\volo.abp.languagemanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.languagemanagement.web\\4.3.0\\volo.abp.languagemanagement.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ldap\\4.3.0\\volo.abp.ldap.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization\\4.3.0\\volo.abp.localization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\4.3.0\\volo.abp.localization.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.minify\\4.3.0\\volo.abp.minify.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\4.3.0\\volo.abp.multitenancy.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\4.3.0\\volo.abp.objectextending.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\4.3.0\\volo.abp.objectmapping.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.application\\4.3.0\\volo.abp.permissionmanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.application.contracts\\4.3.0\\volo.abp.permissionmanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain\\4.3.0\\volo.abp.permissionmanagement.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identity\\4.3.0\\volo.abp.permissionmanagement.domain.identity.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.identityserver\\4.3.0\\volo.abp.permissionmanagement.domain.identityserver.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.shared\\4.3.0\\volo.abp.permissionmanagement.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.entityframeworkcore\\4.3.0\\volo.abp.permissionmanagement.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.httpapi\\4.3.0\\volo.abp.permissionmanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.web\\4.3.0\\volo.abp.permissionmanagement.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\4.3.0\\volo.abp.security.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\4.3.0\\volo.abp.serialization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application\\4.3.0\\volo.abp.settingmanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application.contracts\\4.3.0\\volo.abp.settingmanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.domain\\4.3.0\\volo.abp.settingmanagement.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.domain.shared\\4.3.0\\volo.abp.settingmanagement.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.entityframeworkcore\\4.3.0\\volo.abp.settingmanagement.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.httpapi\\4.3.0\\volo.abp.settingmanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.web\\4.3.0\\volo.abp.settingmanagement.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\4.3.0\\volo.abp.settings.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.sms\\4.3.0\\volo.abp.sms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\4.3.0\\volo.abp.specifications.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.swashbuckle\\4.3.0\\volo.abp.swashbuckle.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.application\\4.3.0\\volo.abp.texttemplatemanagement.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.application.contracts\\4.3.0\\volo.abp.texttemplatemanagement.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.domain\\4.3.0\\volo.abp.texttemplatemanagement.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.domain.shared\\4.3.0\\volo.abp.texttemplatemanagement.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.entityframeworkcore\\4.3.0\\volo.abp.texttemplatemanagement.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.httpapi\\4.3.0\\volo.abp.texttemplatemanagement.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplatemanagement.web\\4.3.0\\volo.abp.texttemplatemanagement.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.texttemplating\\4.3.0\\volo.abp.texttemplating.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\4.3.0\\volo.abp.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\4.3.0\\volo.abp.timing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\4.3.0\\volo.abp.ui.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui.navigation\\4.3.0\\volo.abp.ui.navigation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\4.3.0\\volo.abp.uow.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.abstractions\\4.3.0\\volo.abp.users.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain\\4.3.0\\volo.abp.users.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\4.3.0\\volo.abp.users.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.entityframeworkcore\\4.3.0\\volo.abp.users.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\4.3.0\\volo.abp.validation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation.abstractions\\4.3.0\\volo.abp.validation.abstractions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\4.3.0\\volo.abp.virtualfilesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.domain\\4.3.0\\volo.saas.domain.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.domain.shared\\4.3.0\\volo.saas.domain.shared.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.entityframeworkcore\\4.3.0\\volo.saas.entityframeworkcore.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.host.application\\4.3.0\\volo.saas.host.application.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.host.application.contracts\\4.3.0\\volo.saas.host.application.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.host.httpapi\\4.3.0\\volo.saas.host.httpapi.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.saas.host.web\\4.3.0\\volo.saas.host.web.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\8.1.2\\yamldotnet.8.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\z.entityframework.extensions.efcore\\5.1.41\\z.entityframework.extensions.efcore.5.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\z.entityframework.plus.efcore\\5.1.41\\z.entityframework.plus.efcore.5.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\z.expressions.eval\\4.0.45\\z.expressions.eval.4.0.45.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zxing.net\\0.16.10\\zxing.net.0.16.10.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.AspNet.Mvc 5.2.7' was restored using '.NETPortable,Version=v0.0,Profile=Profile259, .NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8' instead of the project target framework 'net5.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "libraryId": "Microsoft.AspNet.Mvc", "targetGraphs": ["net5.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.AspNet.Razor 3.2.7' was restored using '.NETPortable,Version=v0.0,Profile=Profile259, .NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8' instead of the project target framework 'net5.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "libraryId": "Microsoft.AspNet.Razor", "targetGraphs": ["net5.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.AspNet.WebPages 3.2.7' was restored using '.NETPortable,Version=v0.0,Profile=Profile259, .NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8' instead of the project target framework 'net5.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "libraryId": "Microsoft.AspNet.WebPages", "targetGraphs": ["net5.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'Microsoft.Web.Infrastructure 1.0.0' was restored using '.NETPortable,Version=v0.0,Profile=Profile259, .NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8' instead of the project target framework 'net5.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\src\\AOMS.Web\\AOMS.Web.csproj", "libraryId": "Microsoft.Web.Infrastructure", "targetGraphs": ["net5.0"]}]}