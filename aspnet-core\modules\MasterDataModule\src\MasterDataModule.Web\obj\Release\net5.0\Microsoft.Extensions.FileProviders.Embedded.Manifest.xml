﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="Pages"><Directory Name="MasterDataModule"><Directory Name="Airlines"><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Airlines.index.js</ResourcePath></File></Directory><Directory Name="Authorizes"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Authorizes.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Authorizes.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Authorizes.index.js</ResourcePath></File></Directory><Directory Name="DestManagements"><Directory Name="Apccs"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.Apccs.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.Apccs.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.Apccs.index.js</ResourcePath></File></Directory><File Name="ImportExcelModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.ImportExcelModal.js</ResourcePath></File><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.DestManagements.index.js</ResourcePath></File></Directory><Directory Name="Drivers"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Drivers.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Drivers.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Drivers.index.js</ResourcePath></File></Directory><Directory Name="Ecus"><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Ecus.index.js</ResourcePath></File></Directory><Directory Name="ErtsRemoteTransitSheds"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.ErtsRemoteTransitSheds.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.ErtsRemoteTransitSheds.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.ErtsRemoteTransitSheds.index.js</ResourcePath></File></Directory><Directory Name="FlightSchedules"><File Name="AddFlightNumberModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FlightSchedules.AddFlightNumberModal.js</ResourcePath></File><File Name="Details.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FlightSchedules.Details.js</ResourcePath></File><File Name="EditFlightNumberModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FlightSchedules.EditFlightNumberModal.js</ResourcePath></File><File Name="Search.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FlightSchedules.Search.js</ResourcePath></File></Directory><Directory Name="FwbmFwbMasters"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FwbmFwbMasters.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FwbmFwbMasters.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.FwbmFwbMasters.index.js</ResourcePath></File></Directory><Directory Name="GoodsCategories"><File Name="AddGoodsCategory.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GoodsCategories.AddGoodsCategory.js</ResourcePath></File><File Name="Index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GoodsCategories.Index.js</ResourcePath></File></Directory><Directory Name="GroupCategories"><File Name="AddCategory.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GroupCategories.AddCategory.js</ResourcePath></File><File Name="AddGroupCategory.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GroupCategories.AddGroupCategory.js</ResourcePath></File><File Name="AddSetCategory.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GroupCategories.AddSetCategory.js</ResourcePath></File><File Name="Index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.GroupCategories.Index.js</ResourcePath></File></Directory><Directory Name="Kunds"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Kunds.createModal.js</ResourcePath></File><File Name="detailModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Kunds.detailModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Kunds.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Kunds.index.js</ResourcePath></File></Directory><Directory Name="LocationsMasters"><Directory Name="PhysicalLocations"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.PhysicalLocations.createModal.js</ResourcePath></File><File Name="createMultipleModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.PhysicalLocations.createMultipleModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.PhysicalLocations.editModal.js</ResourcePath></File></Directory><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.createModal.js</ResourcePath></File><File Name="details.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.details.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.index.js</ResourcePath></File><File Name="qrCode.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.LocationsMasters.qrCode.js</ResourcePath></File></Directory><Directory Name="Mitas"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Mitas.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Mitas.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Mitas.index.js</ResourcePath></File></Directory><Directory Name="PrintLabels"><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.PrintLabels.index.js</ResourcePath></File></Directory><Directory Name="StatusMasterObjects"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.StatusMasterObjects.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.StatusMasterObjects.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.StatusMasterObjects.index.js</ResourcePath></File></Directory><Directory Name="Vehicles"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Vehicles.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Vehicles.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.Vehicles.index.js</ResourcePath></File></Directory><Directory Name="WareHousePickups"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHousePickups.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHousePickups.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHousePickups.index.js</ResourcePath></File></Directory><Directory Name="WareHouses"><Directory Name="SetPermission"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHouses.SetPermission.createModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHouses.SetPermission.index.js</ResourcePath></File></Directory><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHouses.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHouses.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WareHouses.index.js</ResourcePath></File></Directory><Directory Name="WorkingPositionTitles"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositionTitles.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositionTitles.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositionTitles.index.js</ResourcePath></File></Directory><Directory Name="WorkingPositions"><File Name="createModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositions.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositions.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>MasterDataModule.Web.Pages.MasterDataModule.WorkingPositions.index.js</ResourcePath></File></Directory></Directory></Directory></FileSystem></Manifest>