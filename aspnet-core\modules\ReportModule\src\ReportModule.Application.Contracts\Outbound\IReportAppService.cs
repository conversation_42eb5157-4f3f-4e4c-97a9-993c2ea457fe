using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;


namespace ReportModule.Outbound
{
    /// <summary>
    /// Class chứa kết quả của các hàm trả về file excel/pdf
    /// </summary>
    public class FileResult
    {
        /// <summary>
        /// Tên file trả về (không bao gồm đường dẫn)
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Loại file (mime type)
        /// Mặc định là excel mine type
        /// </summary>
        public string MimeType { get; set; } = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

        /// <summary>
        /// Nội dung file Excel/PDF
        /// </summary>
        public IRemoteStreamContent FileContent { get; set; }
    }

    public interface IReportAppService : IApplicationService
    {
        // BC khai thác ngày (ca trực)
        Task<IRemoteStreamContent> DownloadDailyMiningReportAsync(DownloadDailyMiningReportInput input);

        // BC 2: List DO về kho
        Task<IRemoteStreamContent> DownloadListDOVeKhoAsync(DownloadListDOVeKhoInput input);

        // BC sổ tổng hợp hàng hóa
        Task<IRemoteStreamContent> DownloadSoTongHopHHXKAsync(DownloadSoTongHopHHXKInput input);

        // BC Biên bản nhận và vận chuyển hàng
        Task<IRemoteStreamContent> DownloadBBNhanVaVanChuyenHangAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // BC Biên bản cân hàng
        Task<IRemoteStreamContent> DownloadBBCanHangAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // Biên bản nhận và vận chuyển hàng theo DO (màn hình Outbound > Truck Unloading > Detail)
        Task<IRemoteStreamContent> DownloadBBNhanHangDOAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // Biên bản cân hàng (màn hình Outbound > Truck Unloading > Detail)
        Task<IRemoteStreamContent> DownloadBBCanHangDetailAsync(DownloadBBNhanVaVanChuyenHangInput input);

        Task<string> CountTruckUnloadingForReport(long truckId);

        // Biên bản nhận hàng DO phiên bản 2 - trả về cả số biên bản và file content
        Task<FileResult> DownloadBBNhanHangDO2Async(DownloadBBNhanVaVanChuyenHangInput input);

        Task<string> CountTruckUnloadingForReport2(long truckId);
    }
}
