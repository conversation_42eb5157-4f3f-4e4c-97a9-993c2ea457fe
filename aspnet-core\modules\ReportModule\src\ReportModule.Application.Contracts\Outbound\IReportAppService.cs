using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using ReportModule.Outbound.Dtos;

namespace ReportModule.Outbound
{
    public interface IReportAppService : IApplicationService
    {
        // BC khai thác ngày (ca trực)
        Task<IRemoteStreamContent> DownloadDailyMiningReportAsync(DownloadDailyMiningReportInput input);

        // BC 2: List DO về kho
        Task<IRemoteStreamContent> DownloadListDOVeKhoAsync(DownloadListDOVeKhoInput input);

        // BC sổ tổng hợp hàng hóa
        Task<IRemoteStreamContent> DownloadSoTongHopHHXKAsync(DownloadSoTongHopHHXKInput input);

        // BC Biên bản nhận và vận chuyển hàng
        Task<IRemoteStreamContent> DownloadBBNhanVaVanChuyenHangAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // BC Biên bản cân hàng
        Task<IRemoteStreamContent> DownloadBBCanHangAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // Biên bản nhận và vận chuyển hàng theo DO (màn hình Outbound > Truck Unloading > Detail)
        Task<IRemoteStreamContent> DownloadBBNhanHangDOAsync(DownloadBBNhanVaVanChuyenHangInput input);

        // Biên bản cân hàng (màn hình Outbound > Truck Unloading > Detail)
        Task<IRemoteStreamContent> DownloadBBCanHangDetailAsync(DownloadBBNhanVaVanChuyenHangInput input);

        Task<string> CountTruckUnloadingForReport(long truckId);

        Task<FileResult> DownloadBBNhanHangDO2Async(DownloadBBNhanVaVanChuyenHangInput input);
        Task<string> CountTruckUnloadingForReport2(long truckId);
    }
}
