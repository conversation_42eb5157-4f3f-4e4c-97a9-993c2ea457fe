{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.EntityFrameworkCore\\Volo.FileManagement.EntityFrameworkCore.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj", "projectName": "Volo.FileManagement.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[4.5.3.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Features": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\Volo.FileManagement.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\Volo.FileManagement.Domain.csproj", "projectName": "Volo.FileManagement.Domain", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\Volo.FileManagement.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain.Shared\\Volo.FileManagement.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[4.5.3.3, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.AutoMapper": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.BlobStoring": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.EntityFrameworkCore\\Volo.FileManagement.EntityFrameworkCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.EntityFrameworkCore\\Volo.FileManagement.EntityFrameworkCore.csproj", "projectName": "Volo.FileManagement.EntityFrameworkCore", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.EntityFrameworkCore\\Volo.FileManagement.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\Volo.FileManagement.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.FileManagement\\src\\Volo.FileManagement.Domain\\Volo.FileManagement.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"EPPlus": {"target": "Package", "version": "[4.5.3.3, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.EntityFrameworkCore": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}}}