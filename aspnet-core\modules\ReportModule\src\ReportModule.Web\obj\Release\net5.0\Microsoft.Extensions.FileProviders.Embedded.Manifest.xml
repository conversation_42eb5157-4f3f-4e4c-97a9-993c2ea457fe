﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="Pages"><Directory Name="ReportModule"><Directory Name="Bonded"><File Name="Index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Bonded.Index.js</ResourcePath></File><File Name="InventoryReport.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Bonded.InventoryReport.js</ResourcePath></File><File Name="InventorySummaryReport.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Bonded.InventorySummaryReport.js</ResourcePath></File></Directory><Directory Name="General"><File Name="Index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.General.Index.js</ResourcePath></File><File Name="InventoryReport.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.General.InventoryReport.js</ResourcePath></File><File Name="InventorySummaryReport.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.General.InventorySummaryReport.js</ResourcePath></File></Directory><Directory Name="Inbound"><File Name="index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Inbound.index.js</ResourcePath></File></Directory><Directory Name="Outbound"><File Name="index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Outbound.index.js</ResourcePath></File></Directory><Directory Name="Reports"><File Name="ReportInputModal.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Reports.ReportInputModal.js</ResourcePath></File><File Name="createModal.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Reports.createModal.js</ResourcePath></File><File Name="editModal.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Reports.editModal.js</ResourcePath></File><File Name="index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Reports.index.js</ResourcePath></File></Directory><Directory Name="Sea"><File Name="index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Sea.index.js</ResourcePath></File></Directory><Directory Name="Trays"><File Name="GoodsSecondaryConsumption.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Trays.GoodsSecondaryConsumption.js</ResourcePath></File><File Name="Index.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Trays.Index.js</ResourcePath></File><File Name="ReportInventory.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Trays.ReportInventory.js</ResourcePath></File><File Name="ReportSDV.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Trays.ReportSDV.js</ResourcePath></File><File Name="ReportSeal.js"><ResourcePath>ReportModule.Web.Pages.ReportModule.Trays.ReportSeal.js</ResourcePath></File></Directory></Directory></Directory></FileSystem></Manifest>