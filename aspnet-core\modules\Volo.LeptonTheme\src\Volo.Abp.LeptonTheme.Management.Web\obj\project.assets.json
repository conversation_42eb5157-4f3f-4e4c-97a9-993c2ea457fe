{"version": 3, "targets": {"net5.0": {"AutoMapper/10.1.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "compile": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}}, "JetBrains.Annotations/2020.3.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Microsoft.AspNetCore.Authorization/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.JsonPatch/5.0.5": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/5.0.5": {"type": "package", "compile": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "5.0.5", "Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.5", "Microsoft.CodeAnalysis.Razor": "5.0.5"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "5.0.5", "Microsoft.CodeAnalysis.Razor": "5.0.5", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"buildTransitive/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets": {}}}, "Microsoft.AspNetCore.Mvc.Versioning/5.0.0": {"type": "package", "compile": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Razor.Language/5.0.5": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/3.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.0.0", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[3.8.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/5.0.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.5", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props": {}, "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props": {}, "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets": {}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/5.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Localization.Abstractions": "5.0.5", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"type": "package", "compile": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Nito.AsyncEx.Context/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Coordination/5.1.0": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.0", "Nito.Collections.Deque": "1.1.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.0": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.0"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Collections.Deque/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.4.0"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "NUglify/1.13.8": {"type": "package", "compile": {"lib/netstandard2.0/NUglify.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NUglify.dll": {"related": ".xml"}}}, "Portable.BouncyCastle/1.8.5.2": {"type": "package", "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"related": ".xml"}}}, "Scriban/3.6.0": {"type": "package", "compile": {"lib/net5.0/Scriban.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Scriban.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "System.CodeDom/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.2.9": {"type": "package", "compile": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.Management/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Registry": "4.7.0", "System.CodeDom": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TimeZoneConverter/3.4.0": {"type": "package", "compile": {"lib/netstandard2.0/TimeZoneConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"related": ".xml"}}}, "Volo.Abp.ApiVersioning.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Authorization": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Http": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Uow": "4.3.0", "Volo.Abp.Validation": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Volo.Abp.AspNetCore.Mvc/4.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "5.0.5", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "5.0.5", "Microsoft.AspNetCore.Mvc.Versioning": "5.0.0", "Volo.Abp.ApiVersioning.Abstractions": "4.3.0", "Volo.Abp.AspNetCore": "4.3.0", "Volo.Abp.AspNetCore.Mvc.Contracts": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.UI.Navigation": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application.Contracts": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI/4.3.0": {"type": "package", "dependencies": {"NUglify": "1.13.8", "Volo.Abp.AspNetCore.Mvc": "4.3.0", "Volo.Abp.UI.Navigation": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"related": ".pdb;.Views.pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"related": ".pdb;.Views.pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "4.3.0", "Volo.Abp.Minify": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Packages": "4.3.0", "Volo.Abp.AspNetCore.Mvc.UI.Widgets": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"related": ".pdb;.Views.pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"related": ".pdb;.Views.pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"related": ".pdb;.Views.pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"related": ".pdb;.Views.pdb;.xml"}}}, "Volo.Abp.Auditing/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "5.0.5", "Volo.Abp.MultiTenancy": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AutoMapper/4.3.0": {"type": "package", "dependencies": {"AutoMapper": "10.1.1", "Volo.Abp.Auditing": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundJobs.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Json": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Commercial.Core/4.3.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.3", "Portable.BouncyCastle": "1.8.5.2", "System.Management": "4.7.0", "Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Commercial.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Commercial.Core.dll": {"related": ".xml"}}}, "Volo.Abp.Core/4.3.0": {"type": "package", "dependencies": {"JetBrains.Annotations": "2020.3.0", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.1", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Localization": "5.0.5", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Nito.AsyncEx.Context": "5.1.0", "Nito.AsyncEx.Coordination": "5.1.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Linq.Dynamic.Core": "1.2.9", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Data/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.Ddd.Application.Contracts": "4.3.0", "Volo.Abp.Ddd.Domain": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.GlobalFeatures": "4.3.0", "Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Security": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "4.3.0", "Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus": "4.3.0", "Volo.Abp.ExceptionHandling": "4.3.0", "Volo.Abp.Guids": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.ObjectMapping": "4.3.0", "Volo.Abp.Specifications": "4.3.0", "Volo.Abp.Threading": "4.3.0", "Volo.Abp.Timing": "4.3.0", "Volo.Abp.Uow": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Emailing/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.TextTemplating": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Emailing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ExceptionHandling/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Features/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.GlobalFeatures/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "4.3.0", "Volo.Abp.Core": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Guids/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Http.Abstractions": "4.3.0", "Volo.Abp.Json": "4.3.0", "Volo.Abp.Minify": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json/4.3.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Volo.Abp.ObjectExtending": "4.3.0", "Volo.Abp.Timing": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Settings": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Minify/4.3.0": {"type": "package", "dependencies": {"NUglify": "1.13.8", "Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Data": "4.3.0", "Volo.Abp.EventBus.Abstractions": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectExtending/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectMapping/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Security/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.SettingManagement.Application.Contracts/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.Emailing": "4.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Volo.Abp.Localization": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.SettingManagement.HttpApi/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc": "4.3.0", "Volo.Abp.SettingManagement.Application.Contracts": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.SettingManagement.HttpApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.SettingManagement.HttpApi.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.SettingManagement.Web/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.SettingManagement.Domain.Shared": "4.3.0", "Volo.Abp.SettingManagement.HttpApi": "4.3.0"}, "compile": {"lib/net5.0/Volo.Abp.SettingManagement.Web.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.SettingManagement.Web.dll": {"related": ".pdb;.Views.pdb;.xml"}}, "runtime": {"lib/net5.0/Volo.Abp.SettingManagement.Web.Views.dll": {"related": ".pdb"}, "lib/net5.0/Volo.Abp.SettingManagement.Web.dll": {"related": ".pdb;.Views.pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Volo.Abp.Settings/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.MultiTenancy": "4.3.0", "Volo.Abp.Security": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Specifications/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.TextTemplating/4.3.0": {"type": "package", "dependencies": {"Scriban": "3.6.0", "Volo.Abp.Localization.Abstractions": "4.3.0", "Volo.Abp.VirtualFileSystem": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.TextTemplating.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.TextTemplating.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Threading/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Timing/4.3.0": {"type": "package", "dependencies": {"TimeZoneConverter": "3.4.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Settings": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.ExceptionHandling": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI.Navigation/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "4.3.0", "Volo.Abp.UI": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Uow/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation.Abstractions": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation.Abstractions/4.3.0": {"type": "package", "dependencies": {"Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.VirtualFileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "5.0.0", "Microsoft.Extensions.FileProviders.Embedded": "5.0.5", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Volo.Abp.Core": "4.3.0"}, "compile": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.LeptonTheme.Management.Application.Contracts/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.0", "dependencies": {"Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Ddd.Application": "4.3.0", "Volo.Abp.LeptonTheme.Management.Domain.Shared": "1.0.0"}, "compile": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.Application.Contracts.dll": {}}, "runtime": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.Application.Contracts.dll": {}}}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.Features": "4.3.0", "Volo.Abp.Localization": "4.3.0", "Volo.Abp.Validation": "4.3.0"}, "compile": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.Domain.Shared.dll": {}}, "runtime": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.Domain.Shared.dll": {}}}, "Volo.Abp.LeptonTheme.Management.HttpApi/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"Volo.Abp.AspNetCore.Mvc": "4.3.0", "Volo.Abp.Commercial.Core": "4.3.0", "Volo.Abp.LeptonTheme.Management.Application.Contracts": "1.0.0"}, "compile": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.HttpApi.dll": {}}, "runtime": {"bin/placeholder/Volo.Abp.LeptonTheme.Management.HttpApi.dll": {}}}}}, "libraries": {"AutoMapper/10.1.1": {"sha512": "uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "type": "package", "path": "automapper/10.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.10.1.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/net461/AutoMapper.dll", "lib/net461/AutoMapper.xml", "lib/netstandard2.0/AutoMapper.dll", "lib/netstandard2.0/AutoMapper.xml"]}, "JetBrains.Annotations/2020.3.0": {"sha512": "FnX06vtxuoZnhZdR6UHt5kJ7HUC/syODfGLnhPDn1x5sXvvepNyCl4jMtPUzJfsPWh7q0Jo+AIYz5xaVbbyikA==", "type": "package", "path": "jetbrains.annotations/2020.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2020.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Microsoft.AspNetCore.Authorization/5.0.5": {"sha512": "pQSx1MrLJlKwlEclliO9aUwKxe9EKI2Mff39VE1t5VYOjqsMyI2ujWKGI6XAUsnmC0Bta67GZ1k4DbQZd7tJKg==", "type": "package", "path": "microsoft.aspnetcore.authorization/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Authorization.dll", "lib/net461/Microsoft.AspNetCore.Authorization.xml", "lib/net5.0/Microsoft.AspNetCore.Authorization.dll", "lib/net5.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.5.0.5.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.JsonPatch/5.0.5": {"sha512": "ViYrCnQFWP3BHVyq73YYe7OijodEp0qBt3l0H3tQtdGB83fb/uEBPGXGrSvfCLkLJPa/Rk4/ZH3oEQcQao2VUg==", "type": "package", "path": "microsoft.aspnetcore.jsonpatch/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.JsonPatch.dll", "lib/net461/Microsoft.AspNetCore.JsonPatch.xml", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.xml", "microsoft.aspnetcore.jsonpatch.5.0.5.nupkg.sha512", "microsoft.aspnetcore.jsonpatch.nuspec"]}, "Microsoft.AspNetCore.Metadata/5.0.5": {"sha512": "SD+puPsFLQXWwzoMiXv8lpFGHyZg0gKp3OP2EXg3eRlwJQOMaUGMIqbCdfHGR4MBaUZtqY/tU68H1bzb6+FSxA==", "type": "package", "path": "microsoft.aspnetcore.metadata/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.AspNetCore.Metadata.dll", "lib/net461/Microsoft.AspNetCore.Metadata.xml", "lib/net5.0/Microsoft.AspNetCore.Metadata.dll", "lib/net5.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.5.0.5.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/5.0.5": {"sha512": "3Vd2hbJwrHFOk6dYpqFQ10BBLeJvMPmbx3hxqH5erxSlvFOufnq3C9Nnz+hPpaqazRAQcOgkAWGvBsB3AM2XlQ==", "type": "package", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll", "lib/net5.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.xml", "microsoft.aspnetcore.mvc.newtonsoftjson.5.0.5.nupkg.sha512", "microsoft.aspnetcore.mvc.newtonsoftjson.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/5.0.5": {"sha512": "wj6Q8mcbL8xDS47axjojDiB0nBhRekBl2Ng/v9Hnk0TxxqD5eMxn6OeKELJjLkQqW6P4vCmdAiBEYLGAr5P/8g==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.extensions/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll", "microsoft.aspnetcore.mvc.razor.extensions.5.0.5.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.extensions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/5.0.5": {"sha512": "nzYMwt6zGvpWFD4RdMnPyJGzHgXusCjx6jOqGyJ+goZHi9QhzS94dUOyQ5Q3aULAC4Qh2B0p5Gx7V+7RJdr1XA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "build/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "buildTransitive/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll", "lib/net5.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.xml", "microsoft.aspnetcore.mvc.razor.runtimecompilation.5.0.5.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.runtimecompilation.nuspec"]}, "Microsoft.AspNetCore.Mvc.Versioning/5.0.0": {"sha512": "mN9IARvNpHMBD2/oGmp5Bxp1Dg45Hfcp+LWaAyTtL2HisWLMOIcf0Ox1qW9IvCvdbHM+2A9dWEInhiqBsNxsJA==", "type": "package", "path": "microsoft.aspnetcore.mvc.versioning/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "icon.png", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.pdb", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.xml", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.dll", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.pdb", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.xml", "microsoft.aspnetcore.mvc.versioning.5.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.versioning.nuspec"]}, "Microsoft.AspNetCore.Razor.Language/5.0.5": {"sha512": "wh4JC/1sWxythiEEUKacWqZWNWnx6Kbmpj6FhYc28zV2VAV1+c9Ii1y7fmRefZciJpI/b25bm7c8icT72g2O9g==", "type": "package", "path": "microsoft.aspnetcore.razor.language/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll", "microsoft.aspnetcore.razor.language.5.0.5.nupkg.sha512", "microsoft.aspnetcore.razor.language.nuspec"]}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {"sha512": "ojG5pGAhTPmjxRGTNvuszO3H8XPZqksDwr9xLd4Ae/JBjZZdl6GuoLk7uLMf+o7yl5wO0TAqoWcEKkEWqrZE5g==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/3.8.0": {"sha512": "8YTZ7GpsbTdC08DITx7/kwV0k4SC6cbBAFqc13cOm5vKJZcEIAh51tNSyGSkWisMgYCr96B2wb5Zri1bsla3+g==", "type": "package", "path": "microsoft.codeanalysis.common/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.3.8.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"sha512": "hKqFCUSk9TIMBDjiYMF8/ZfK9p9mzpU+slM73CaCHu4ctfkoqJGHLQhyT8wvrYsIg+ufrUWBF8hcJYmyr5rc5Q==", "type": "package", "path": "microsoft.codeanalysis.csharp/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.Razor/5.0.5": {"sha512": "UYfBFE6dWkFXAIZlc/p8DYawHAPdfPdWBcX5Zrvx8WxYV74qdomARTyDDArKfbfm2MMi2oIReNjabPrBz4T42w==", "type": "package", "path": "microsoft.codeanalysis.razor/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll", "microsoft.codeanalysis.razor.5.0.5.nupkg.sha512", "microsoft.codeanalysis.razor.nuspec"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration/5.0.0": {"sha512": "LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "type": "package", "path": "microsoft.extensions.configuration/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"sha512": "ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"sha512": "Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Binder.dll", "lib/net461/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"sha512": "OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "type": "package", "path": "microsoft.extensions.configuration.commandline/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net461/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"sha512": "fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"sha512": "rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"sha512": "Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "type": "package", "path": "microsoft.extensions.configuration.json/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"sha512": "+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "build/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection/5.0.1": {"sha512": "//mDNrYeiJ0eh/awFhDFJQzkRVra/njU5Y4fyK7X29g5HScrzbUkKOKlyTtygthcGFt4zNC8G5CFCjb/oizomA==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyModel/5.0.0": {"sha512": "umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "type": "package", "path": "microsoft.extensions.dependencymodel/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/net451/Microsoft.Extensions.DependencyModel.xml", "lib/net461/Microsoft.Extensions.DependencyModel.dll", "lib/net461/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"sha512": "iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Composite/5.0.0": {"sha512": "0IoXXfkgKpYJB1t2lC0jPXAxuaywRNc9y2Mq96ZZNKBthL38vusa2UK73+Bm6Kq/9a5xNHJS6NhsSN+i5TEtkA==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net461/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"sha512": "sSf5oTuE/BYju9hqvwL6CSwArv76mONplyVpYV2J8Il/m2mtuabx2o3YmTeO8aa5+2JaFWZlOX+2X3fWYEp79w==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets", "lib/net461/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net461/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.5.0.17.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec", "tasks/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll"]}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"sha512": "1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"sha512": "ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"sha512": "cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Localization/5.0.5": {"sha512": "XBX02xG84g6q+sGnUnBLuRHZt+ZfKIKeY+oLsSqSb/0Hy53lmCGiufCpMH4TZVqmpT3xmFb47YKhA4ROt0SwVQ==", "type": "package", "path": "microsoft.extensions.localization/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Localization.dll", "lib/net461/Microsoft.Extensions.Localization.xml", "lib/net5.0/Microsoft.Extensions.Localization.dll", "lib/net5.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.5.0.5.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/5.0.5": {"sha512": "NIKR1AE1gt4QfThQCxJnzQIYcp0sDijX61GtkrgDce0kqatAME7oZDnYQAZTYlm/QYXKNYqu+S58BW53QRM7oQ==", "type": "package", "path": "microsoft.extensions.localization.abstractions/5.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net461/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net5.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net5.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.5.0.5.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"sha512": "280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Newtonsoft.Json.Bson/1.0.2": {"sha512": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "type": "package", "path": "newtonsoft.json.bson/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net45/Newtonsoft.Json.Bson.dll", "lib/net45/Newtonsoft.Json.Bson.pdb", "lib/net45/Newtonsoft.Json.Bson.xml", "lib/netstandard1.3/Newtonsoft.Json.Bson.dll", "lib/netstandard1.3/Newtonsoft.Json.Bson.pdb", "lib/netstandard1.3/Newtonsoft.Json.Bson.xml", "lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "lib/netstandard2.0/Newtonsoft.Json.Bson.pdb", "lib/netstandard2.0/Newtonsoft.Json.Bson.xml", "newtonsoft.json.bson.1.0.2.nupkg.sha512", "newtonsoft.json.bson.nuspec"]}, "Nito.AsyncEx.Context/5.1.0": {"sha512": "EE7M37c5E/kvulzEkpUR6v1AnK34b2wysOLJHSjl78p/3hL7grte0XCPRqCfLZDwq98AD9GHMTCRfZy7TEeHhw==", "type": "package", "path": "nito.asyncex.context/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.0.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Coordination/5.1.0": {"sha512": "Nv+oA+cSxidjOImiKcz2FJgMIDxiK0A6xormKmsUklUBjTNqQpjtdJsACMgTQG56PkTHdbMi5QijPTTUsmcCeg==", "type": "package", "path": "nito.asyncex.coordination/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Coordination.dll", "lib/netstandard1.3/Nito.AsyncEx.Coordination.xml", "lib/netstandard2.0/Nito.AsyncEx.Coordination.dll", "lib/netstandard2.0/Nito.AsyncEx.Coordination.xml", "nito.asyncex.coordination.5.1.0.nupkg.sha512", "nito.asyncex.coordination.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.0": {"sha512": "tU3Ib4zs8ivM+uS8n7F7ReWZlA3mODyLqwPE+v+WJI94hZ8xLXl+a9npfj/IcmeXo9a6fGKLWkswKQHOeTWqwA==", "type": "package", "path": "nito.asyncex.tasks/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.0.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Collections.Deque/1.1.0": {"sha512": "RXHe531Oaw2IathDr0Q2kbid0iuudBxtgZsfBZ2eUPuFI8I1P7HMiuUeaIefqYykcDYFTDQsFAPAljduIjihLA==", "type": "package", "path": "nito.collections.deque/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard1.0/Nito.Collections.Deque.dll", "lib/netstandard1.0/Nito.Collections.Deque.xml", "lib/netstandard2.0/Nito.Collections.Deque.dll", "lib/netstandard2.0/Nito.Collections.Deque.xml", "nito.collections.deque.1.1.0.nupkg.sha512", "nito.collections.deque.nuspec"]}, "Nito.Disposables/2.2.0": {"sha512": "QcL+uBwUCEoK8GKp/WzjdCiG8/3G1WLlVNJgLJUNG7bIIVAcEV+Mro4s53VT4Nd8xMSplv0gy+Priw44vRvLaA==", "type": "package", "path": "nito.disposables/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.0.nupkg.sha512", "nito.disposables.nuspec"]}, "NUglify/1.13.8": {"sha512": "S6ofErWTXnV/5k2GUirnhC+xisauluBki1L0MWBQKGFnSQcMB3B3AbUsDoJE7XVgo4ZgVcqXZ0eMeZieKe1kKA==", "type": "package", "path": "nuglify/1.13.8", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/NUglify.dll", "lib/net35/NUglify.xml", "lib/net40/NUglify.dll", "lib/net40/NUglify.xml", "lib/netstandard1.3/NUglify.dll", "lib/netstandard1.3/NUglify.xml", "lib/netstandard2.0/NUglify.dll", "lib/netstandard2.0/NUglify.xml", "nuglify.1.13.8.nupkg.sha512", "nuglify.nuspec", "nuglify.png"]}, "Portable.BouncyCastle/1.8.5.2": {"sha512": "iLfdr+T33QrDpsvcKRbMVk0PnjqE4hcrbgc6IH+A1BdPAThZlMB9s5TtFANhVVtdS+0oN3yIivgXRPRET7zuZw==", "type": "package", "path": "portable.bouncycastle/1.8.5.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/BouncyCastle.Crypto.dll", "lib/net40/BouncyCastle.Crypto.xml", "lib/netstandard2.0/BouncyCastle.Crypto.dll", "lib/netstandard2.0/BouncyCastle.Crypto.xml", "portable.bouncycastle.1.8.5.2.nupkg.sha512", "portable.bouncycastle.nuspec"]}, "Scriban/3.6.0": {"sha512": "DErH4z5gy+KGznkrH6hqox4p9GNcN47EGrEppb1fTlryDTwt4dIr3zNSwxnU/5fDxhW0ZJsZNWzvK2Ox0lir1w==", "type": "package", "path": "scriban/3.6.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Scriban.props", "build/Scriban.targets", "lib/net5.0/Scriban.dll", "lib/net5.0/Scriban.xml", "lib/netstandard2.0/Scriban.dll", "lib/netstandard2.0/Scriban.xml", "scriban.3.6.0.nupkg.sha512", "scriban.nuspec", "scriban.png", "src/Scriban/Functions/ArrayFunctions.cs", "src/Scriban/Functions/BuiltinFunctions.cs", "src/Scriban/Functions/DateTimeFunctions.cs", "src/Scriban/Functions/HtmlFunctions.cs", "src/Scriban/Functions/IncludeFunction.cs", "src/Scriban/Functions/LiquidBuiltinsFunctions.cs", "src/Scriban/Functions/MathFunctions.cs", "src/Scriban/Functions/ObjectFunctions.cs", "src/Scriban/Functions/RegexFunctions.cs", "src/Scriban/Functions/StringFunctions.cs", "src/Scriban/Functions/TimeSpanFunctions.cs", "src/Scriban/Helpers/BoxHelper.cs", "src/Scriban/Helpers/CharHelper.cs", "src/Scriban/Helpers/FastStack.cs", "src/Scriban/Helpers/InlineList.cs", "src/Scriban/Helpers/MethodImplOptionsPortable.cs", "src/Scriban/Helpers/ReflectionHelper.cs", "src/Scriban/Helpers/StringHelper.cs", "src/Scriban/Helpers/ThrowHelper.cs", "src/Scriban/LogMessageBag.cs", "src/Scriban/Parsing/Lexer.cs", "src/Scriban/Parsing/LexerOptions.cs", "src/Scriban/Parsing/LogMessage.cs", "src/Scriban/Parsing/Parser.Expressions.cs", "src/Scriban/Parsing/Parser.Statements.Liquid.cs", "src/Scriban/Parsing/Parser.Statements.Scriban.cs", "src/Scriban/Parsing/Parser.Statements.cs", "src/Scriban/Parsing/Parser.Terminals.cs", "src/Scriban/Parsing/Parser.cs", "src/Scriban/Parsing/ParserOptions.cs", "src/Scriban/Parsing/ScriptLang.cs", "src/Scriban/Parsing/ScriptMode.cs", "src/Scriban/Parsing/SourceSpan.cs", "src/Scriban/Parsing/TextPosition.cs", "src/Scriban/Parsing/Token.cs", "src/Scriban/Parsing/TokenTextAttribute.cs", "src/Scriban/Parsing/TokenType.cs", "src/Scriban/Parsing/TokenTypeExtensions.cs", "src/Scriban/Parsing/Util.cs", "src/Scriban/Runtime/Accessors/ArrayAccessor.cs", "src/Scriban/Runtime/Accessors/DictionaryAccessor.cs", "src/Scriban/Runtime/Accessors/ListAccessor.cs", "src/Scriban/Runtime/Accessors/NullAccessor.cs", "src/Scriban/Runtime/Accessors/PrimitiveAccessor.cs", "src/Scriban/Runtime/Accessors/ScriptObjectAccessor.cs", "src/Scriban/Runtime/Accessors/StringAccessor.cs", "src/Scriban/Runtime/Accessors/TypedObjectAccessor.cs", "src/Scriban/Runtime/CustomFunction.Generated.cs", "src/Scriban/Runtime/DelegateCustomFunction.cs", "src/Scriban/Runtime/DynamicCustomFunction.cs", "src/Scriban/Runtime/EmptyScriptObject.cs", "src/Scriban/Runtime/IListAccessor.cs", "src/Scriban/Runtime/IObjectAccessor.cs", "src/Scriban/Runtime/IScriptCustomFunction.cs", "src/Scriban/Runtime/IScriptObject.cs", "src/Scriban/Runtime/IScriptOutput.cs", "src/Scriban/Runtime/IScriptTransformable.cs", "src/Scriban/Runtime/ITemplateLoader.cs", "src/Scriban/Runtime/MemberFilterDelegate.cs", "src/Scriban/Runtime/MemberRenamerDelegate.cs", "src/Scriban/Runtime/ScriptArray.cs", "src/Scriban/Runtime/ScriptMemberIgnoreAttribute.cs", "src/Scriban/Runtime/ScriptMemberImportFlags.cs", "src/Scriban/Runtime/ScriptObject.cs", "src/Scriban/Runtime/ScriptObjectExtensions.cs", "src/Scriban/Runtime/ScriptPipeArguments.cs", "src/Scriban/Runtime/ScriptRange.cs", "src/Scriban/Runtime/StandardMemberRenamer.cs", "src/Scriban/Runtime/StringBuilderOutput.cs", "src/Scriban/Runtime/TextWriterOutput.cs", "src/Scriban/ScribanAsync.generated.cs", "src/Scriban/ScribanVisitors.generated.cs", "src/Scriban/ScriptPrinter.cs", "src/Scriban/ScriptPrinterOptions.cs", "src/Scriban/Syntax/Expressions/IScriptTerminal.cs", "src/Scriban/Syntax/Expressions/ScriptAnonymousFunction.cs", "src/Scriban/Syntax/Expressions/ScriptArgumentBinary.cs", "src/Scriban/Syntax/Expressions/ScriptArrayInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptAssignExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptConditionalExpression.cs", "src/Scriban/Syntax/Expressions/ScriptExpression.cs", "src/Scriban/Syntax/Expressions/ScriptFunctionCall.cs", "src/Scriban/Syntax/Expressions/ScriptIndexerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptIsEmptyExpression.cs", "src/Scriban/Syntax/Expressions/ScriptLiteral.cs", "src/Scriban/Syntax/Expressions/ScriptMemberExpression.cs", "src/Scriban/Syntax/Expressions/ScriptNamedArgument.cs", "src/Scriban/Syntax/Expressions/ScriptNestedExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectMember.cs", "src/Scriban/Syntax/Expressions/ScriptPipeCall.cs", "src/Scriban/Syntax/Expressions/ScriptThisExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptVariable.cs", "src/Scriban/Syntax/Expressions/ScriptVariableScope.cs", "src/Scriban/Syntax/IScriptConvertibleFrom.cs", "src/Scriban/Syntax/IScriptConvertibleTo.cs", "src/Scriban/Syntax/IScriptCustomBinaryOperation.cs", "src/Scriban/Syntax/IScriptCustomImplicitMultiplyPrecedence.cs", "src/Scriban/Syntax/IScriptCustomType.cs", "src/Scriban/Syntax/IScriptCustomTypeInfo.cs", "src/Scriban/Syntax/IScriptCustomUnaryOperation.cs", "src/Scriban/Syntax/IScriptNamedArgumentContainer.cs", "src/Scriban/Syntax/IScriptVariablePath.cs", "src/Scriban/Syntax/IScriptVisitorContext.cs", "src/Scriban/Syntax/ScientificFunctionCallRewriter.cs", "src/Scriban/Syntax/ScriptArgumentException.cs", "src/Scriban/Syntax/ScriptCloner.cs", "src/<PERSON>riban/Syntax/ScriptFormatter.cs", "src/<PERSON>riban/Syntax/ScriptFormatterExtensions.cs", "src/Scriban/Syntax/ScriptFormatterFlags.cs", "src/Scriban/Syntax/ScriptFormatterOptions.cs", "src/Scriban/Syntax/ScriptFrontMatter.cs", "src/Scriban/Syntax/ScriptIdentifier.cs", "src/Scriban/Syntax/ScriptKeyword.cs", "src/Scriban/Syntax/ScriptList.cs", "src/Scriban/Syntax/ScriptNode.cs", "src/Scriban/Syntax/ScriptNodeExtensions.cs", "src/Scriban/Syntax/ScriptPage.cs", "src/Scriban/Syntax/ScriptParameterContainerExtensions.cs", "src/Scriban/Syntax/ScriptRewriter.cs", "src/Scriban/Syntax/ScriptRuntimeException.cs", "src/Scriban/Syntax/ScriptStringSlice.cs", "src/Scriban/Syntax/ScriptSyntaxAttribute.cs", "src/Scriban/Syntax/ScriptToken.cs", "src/Scriban/Syntax/ScriptTrivia.cs", "src/Scriban/Syntax/ScriptTriviaType.cs", "src/Scriban/Syntax/ScriptTriviaTypeExtensions.cs", "src/Scriban/Syntax/ScriptTrivias.cs", "src/Scriban/Syntax/ScriptVerbatim.cs", "src/Scriban/Syntax/ScriptVisitor.cs", "src/Scriban/Syntax/Statements/ScriptBlockStatement.cs", "src/Scriban/Syntax/Statements/ScriptBreakStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaptureStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaseStatement.cs", "src/Scriban/Syntax/Statements/ScriptConditionStatement.cs", "src/Scriban/Syntax/Statements/ScriptContinueStatement.cs", "src/Scriban/Syntax/Statements/ScriptElseStatement.cs", "src/Scriban/Syntax/Statements/ScriptEndStatement.cs", "src/Scriban/Syntax/Statements/ScriptEscapeStatement.cs", "src/Scriban/Syntax/Statements/ScriptExpressionStatement.cs", "src/Scriban/Syntax/Statements/ScriptFlowState.cs", "src/Scriban/Syntax/Statements/ScriptForStatement.cs", "src/Scriban/Syntax/Statements/ScriptFunction.cs", "src/Scriban/Syntax/Statements/ScriptIfStatement.cs", "src/Scriban/Syntax/Statements/ScriptImportStatement.cs", "src/Scriban/Syntax/Statements/ScriptLoopStatementBase.cs", "src/Scriban/Syntax/Statements/ScriptNopStatement.cs", "src/Scriban/Syntax/Statements/ScriptParameter.cs", "src/Scriban/Syntax/Statements/ScriptRawStatement.cs", "src/Scriban/Syntax/Statements/ScriptReadOnlyStatement.cs", "src/Scriban/Syntax/Statements/ScriptReturnStatement.cs", "src/Scriban/Syntax/Statements/ScriptStatement.cs", "src/Scriban/Syntax/Statements/ScriptTableRowStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhenStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhileStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhitespaceMode.cs", "src/Scriban/Syntax/Statements/ScriptWithStatement.cs", "src/Scriban/Syntax/Statements/ScriptWrapStatement.cs", "src/Scriban/Template.cs", "src/Scriban/TemplateContext.Helpers.cs", "src/Scriban/TemplateContext.Variables.cs", "src/Scriban/TemplateContext.cs"]}, "System.CodeDom/4.7.0": {"sha512": "Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "type": "package", "path": "system.codedom/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/net461/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.7.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.2.9": {"sha512": "GjR4CCtIkiJSU6N8cidG4aa0ph+HBzFOq3uhLybuq4zjlKy3hjDrGbcEUeBiGpBmUrnUhTAJ5SCZGDoZS7d9SA==", "type": "package", "path": "system.linq.dynamic.core/1.2.9", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "system.linq.dynamic.core.1.2.9.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.Management/4.7.0": {"sha512": "IY+uuGhgzWiCg21i8IvQeY/Z7m1tX8VuPF+ludfn7iTCaccTtJo5HkjZbBEL8kbBubKhAKKtNXr7uMtmAc28Pw==", "type": "package", "path": "system.management/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "ref/net45/_._", "ref/netstandard2.0/System.Management.dll", "ref/netstandard2.0/System.Management.xml", "runtimes/win/lib/net45/_._", "runtimes/win/lib/netcoreapp2.0/System.Management.dll", "runtimes/win/lib/netcoreapp2.0/System.Management.xml", "system.management.4.7.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"sha512": "zOHkQmzPCn5zm/BH+cxC1XbUS3P4Yoi3xzW7eRgVpDR2tPGSzyMZ17Ig1iRkfJuY0nhxkQQde8pgePNiA7z7TQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.5.1": {"sha512": "4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "type": "package", "path": "system.text.encoding.codepages/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.5.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TimeZoneConverter/3.4.0": {"sha512": "4rqXd06uLqaLPGpO+whrZnSTJyG3SG5DEbeldMTvaGit0+gI6XLVb9YMnfM+5wtV0DDM1QPd/QhbE6j5vJCWDw==", "type": "package", "path": "timezoneconverter/3.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/TimeZoneConverter.dll", "lib/net35/TimeZoneConverter.xml", "lib/net40/TimeZoneConverter.dll", "lib/net40/TimeZoneConverter.xml", "lib/net45/TimeZoneConverter.dll", "lib/net45/TimeZoneConverter.xml", "lib/net461/TimeZoneConverter.dll", "lib/net461/TimeZoneConverter.xml", "lib/net471/TimeZoneConverter.dll", "lib/net471/TimeZoneConverter.xml", "lib/netstandard1.1/TimeZoneConverter.dll", "lib/netstandard1.1/TimeZoneConverter.xml", "lib/netstandard1.3/TimeZoneConverter.dll", "lib/netstandard1.3/TimeZoneConverter.xml", "lib/netstandard2.0/TimeZoneConverter.dll", "lib/netstandard2.0/TimeZoneConverter.xml", "timezoneconverter.3.4.0.nupkg.sha512", "timezoneconverter.nuspec"]}, "Volo.Abp.ApiVersioning.Abstractions/4.3.0": {"sha512": "x6dgBSTcOJTUbN9BHCBwzZ61h9ujW6rVaUyNjJ+1wnM+BRI9LhvoXIODA7160i5OKxKsbBlSV4qV45hHGIy47A==", "type": "package", "path": "volo.abp.apiversioning.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.xml", "volo.abp.apiversioning.abstractions.4.3.0.nupkg.sha512", "volo.abp.apiversioning.abstractions.nuspec"]}, "Volo.Abp.AspNetCore/4.3.0": {"sha512": "dGFxH4wgax5SsjHg8rAyBDMa+JX/i9yDLmvQi7uXPN2G3QdPYKgm8VxZecxuA8G8EwPg2Fc+An/7OZUcg2Xsvw==", "type": "package", "path": "volo.abp.aspnetcore/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.dll", "lib/net5.0/Volo.Abp.AspNetCore.pdb", "lib/net5.0/Volo.Abp.AspNetCore.xml", "volo.abp.aspnetcore.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.nuspec"]}, "Volo.Abp.AspNetCore.Mvc/4.3.0": {"sha512": "LFpGvCjrav/PMoWSDXg6bIa1jdPNSVXs9bOZ/4v2CN4DvjRxjPKJXTrDm5VmcyWvi/9eItxBtSQhq89/8Y586w==", "type": "package", "path": "volo.abp.aspnetcore.mvc/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.xml", "volo.abp.aspnetcore.mvc.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.Contracts/4.3.0": {"sha512": "QoWDbsp5Tpm6nhZqJX0d1Sz/XPs1NhEIijzbKH6aJqRypyV7RLimhj7uFflCyB0ZFA5Vl4yhjp7ZehMuEY321A==", "type": "package", "path": "volo.abp.aspnetcore.mvc.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "volo.abp.aspnetcore.mvc.contracts.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.contracts.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI/4.3.0": {"sha512": "s3P9h/YbUD5g5EAcJalce0RZiu/SMeU+DgHHhyPgqK+yNS7kVrLCftwGDRZAlzmLyIETA7Z+oXw+esu7cTHSpA==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Views.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.xml", "volo.abp.aspnetcore.mvc.ui.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/4.3.0": {"sha512": "cmO+1dBQb9oWcU3lgw6UEjptNcS/JL/vU7OOE2/xr1ScUgNQ+q42VY3AYI+49Qu2+1EymmdGpXAP5WTfeUjffw==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bootstrap/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.xml", "volo.abp.aspnetcore.mvc.ui.bootstrap.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bootstrap.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/4.3.0": {"sha512": "8KTKH+b5kmOZFU5TTCSk8ZP195oNJ0Mh5v68ecH7+mhM8zX8HF9qF8kTjGf9Ic9MeHm9ZqtadgSMjAJhO2O01w==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bundling/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.xml", "volo.abp.aspnetcore.mvc.ui.bundling.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bundling.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/4.3.0": {"sha512": "IsBAzY811Xd3Ei31XKkQHiFWy6PiMGWO8xxydyBOaGewfp/ROX9RUrKL+ZDmXyAj0dN8lIgMfi//UUfIMbKe6w==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.xml", "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/4.3.0": {"sha512": "6PWDHGFWuT2ruIFJFriYssY88LnkWDZ2d1XztFOo5kYvc5sxb663Eg3wTsDDMuZW7f977X+3b6CB0Np6TLLPYg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.packages/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.xml", "volo.abp.aspnetcore.mvc.ui.packages.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.packages.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/4.3.0": {"sha512": "6Uuz8O1pkGWP5vIPiC63RyJmNVN2I1XtjXlLhYFvSUnE6DKjgx9GrerIgrHSenx6+4IGuBKFWQdfjmhtGlWIqg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.theme.shared/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Views.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.xml", "volo.abp.aspnetcore.mvc.ui.theme.shared.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.theme.shared.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/4.3.0": {"sha512": "nvefhpGzg/99XvK7d5fUmP8e7bgpUVWY4ip+uMEikaM0USuFqKR3t7YFjY37nDCPwMNBBK3B+XwxX8/ZM6/vGg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.widgets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.Views.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.pdb", "lib/net5.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.xml", "volo.abp.aspnetcore.mvc.ui.widgets.4.3.0.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.widgets.nuspec"]}, "Volo.Abp.Auditing/4.3.0": {"sha512": "zptBxUin6U/LbdRdG6rRn+NyTQIAqN7wqjx3Ui7G9vvTHrRCreqzvuOsPC767HDUlcu+8o3LNJLQCuE981219A==", "type": "package", "path": "volo.abp.auditing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Auditing.dll", "lib/netstandard2.0/Volo.Abp.Auditing.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.xml", "volo.abp.auditing.4.3.0.nupkg.sha512", "volo.abp.auditing.nuspec"]}, "Volo.Abp.Authorization/4.3.0": {"sha512": "9j5U19ntqS8JR+s86xNFm0ieH9CHz/po51d6BHxLTZzJuKbIu9xg453KvhKyuXYYnhp+/afu7Uq5EEi/s7sl9A==", "type": "package", "path": "volo.abp.authorization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.xml", "volo.abp.authorization.4.3.0.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.Authorization.Abstractions/4.3.0": {"sha512": "enL7a1MfJR0yjPVhf2UCgEmmQQ5wNb2GGX3k/iKzW5i//3lcnsDZQLAg17NKcbLc+imQ39RALNTOE4ALZQ4C7Q==", "type": "package", "path": "volo.abp.authorization.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.xml", "volo.abp.authorization.abstractions.4.3.0.nupkg.sha512", "volo.abp.authorization.abstractions.nuspec"]}, "Volo.Abp.AutoMapper/4.3.0": {"sha512": "mqeDGZxdhXFQMvxfM26snT2q0pbA+3OFE4rIoFmNdooK6fE0oWCcRg5vAOuaZNlzMCfpRFtNatSfhwZUXXfYCA==", "type": "package", "path": "volo.abp.automapper/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.AutoMapper.dll", "lib/netstandard2.0/Volo.Abp.AutoMapper.pdb", "lib/netstandard2.0/Volo.Abp.AutoMapper.xml", "volo.abp.automapper.4.3.0.nupkg.sha512", "volo.abp.automapper.nuspec"]}, "Volo.Abp.BackgroundJobs.Abstractions/4.3.0": {"sha512": "9pOV9z7ELSrJ231wXJoAQfr/nKU/h/MSi9uwqiitl2oKewqc5Yp21AoJI8kaFGM3qAnFs2deiYd1eKiJWSvHPw==", "type": "package", "path": "volo.abp.backgroundjobs.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.BackgroundJobs.Abstractions.xml", "volo.abp.backgroundjobs.abstractions.4.3.0.nupkg.sha512", "volo.abp.backgroundjobs.abstractions.nuspec"]}, "Volo.Abp.Commercial.Core/4.3.0": {"sha512": "FCf1InEuqKPE36zhiy5biNtm3N+SlR9sztxLIHCJ5kCueYqFH9Te75Lj+G2dm3fLsE2Fmuvv+zmMBoNIhzeU5g==", "type": "package", "path": "volo.abp.commercial.core/4.3.0", "files": [".nupkg.metadata", "lib/netstandard2.0/Volo.Abp.Commercial.Core.dll", "lib/netstandard2.0/Volo.Abp.Commercial.Core.xml", "volo.abp.commercial.core.4.3.0.nupkg.sha512", "volo.abp.commercial.core.nuspec"]}, "Volo.Abp.Core/4.3.0": {"sha512": "GTXSY8W+AG5sMczOv40AR85eq9a+R/qI4Ci7h853rudprGWkwtfYw9OQnrVd3Xu74WceSTfCDtHWbu2AuJ3Q+w==", "type": "package", "path": "volo.abp.core/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "lib/netstandard2.0/Volo.Abp.Core.xml", "volo.abp.core.4.3.0.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/4.3.0": {"sha512": "2r42Sq03qH9z/B3fUZebbAeiEAPO3poHtzLslBhm4Qjjf9jlhBrkVjqZNkCDggNFNMVzRwWmrzSzIop8q8Crug==", "type": "package", "path": "volo.abp.data/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "lib/netstandard2.0/Volo.Abp.Data.xml", "volo.abp.data.4.3.0.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Application/4.3.0": {"sha512": "CGyrh+hAV6RPCVk+yT+BUa2itmnjbSCkBZBrUhkFvEJior8BvLVbMT9DGOxoDE3pOzeIkcqdk2bog9Wiq19fkg==", "type": "package", "path": "volo.abp.ddd.application/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.xml", "volo.abp.ddd.application.4.3.0.nupkg.sha512", "volo.abp.ddd.application.nuspec"]}, "Volo.Abp.Ddd.Application.Contracts/4.3.0": {"sha512": "ysaifHH09mGhQLxEBTAKu8F/GsKbZmcs78CIulkqliPM9qMtyqIuNDSPq/LvWbyRBfPWb/wFdHiBtcBGj2tZVw==", "type": "package", "path": "volo.abp.ddd.application.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.xml", "volo.abp.ddd.application.contracts.4.3.0.nupkg.sha512", "volo.abp.ddd.application.contracts.nuspec"]}, "Volo.Abp.Ddd.Domain/4.3.0": {"sha512": "Nq5cMx0Uifg7GW8E8NedJZ7jaiPJv2Y9axYht5xbHEJ9gpsy1oovOp2VPKZ5Y5YYJY+M/1dlUEMAjM2LAANfpA==", "type": "package", "path": "volo.abp.ddd.domain/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.xml", "volo.abp.ddd.domain.4.3.0.nupkg.sha512", "volo.abp.ddd.domain.nuspec"]}, "Volo.Abp.Emailing/4.3.0": {"sha512": "mdCYf7O/O/xJE78ZnDLlvrLLbFxje7WcMxhhERBunpD1T7oNKpgS4QeR7kYnZ5Y4ByYmvqE3LVjTrLVf5Mhkcw==", "type": "package", "path": "volo.abp.emailing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Emailing.dll", "lib/netstandard2.0/Volo.Abp.Emailing.pdb", "lib/netstandard2.0/Volo.Abp.Emailing.xml", "volo.abp.emailing.4.3.0.nupkg.sha512", "volo.abp.emailing.nuspec"]}, "Volo.Abp.EventBus/4.3.0": {"sha512": "AjBsTwigVF/LJVJf/5JgwWUX7vRNvHR/HRxst4+mDS/VJZDsgqIZLwDOFnFuA+wKtOW49e9+wuaU/LLat9HPoQ==", "type": "package", "path": "volo.abp.eventbus/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.xml", "volo.abp.eventbus.4.3.0.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.EventBus.Abstractions/4.3.0": {"sha512": "DW2Cr8k/g2kWqxLBEZvBjT/BSpbUo3zw8uSBMaAGAAKn4k7cNp7FC3YESu0C3y45Nd8Lo/3JLc9Iw4NN5uFmSA==", "type": "package", "path": "volo.abp.eventbus.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.xml", "volo.abp.eventbus.abstractions.4.3.0.nupkg.sha512", "volo.abp.eventbus.abstractions.nuspec"]}, "Volo.Abp.ExceptionHandling/4.3.0": {"sha512": "7ht+TOh8YlBZ+06GQ9vXP0FEbCPh7suNqUeKkz034NNNBKYVga9LU6qIpSaquVsM4++8NI/5AZpNh+ocgGhUBw==", "type": "package", "path": "volo.abp.exceptionhandling/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.xml", "volo.abp.exceptionhandling.4.3.0.nupkg.sha512", "volo.abp.exceptionhandling.nuspec"]}, "Volo.Abp.Features/4.3.0": {"sha512": "8pHFDHqOQ54UEDciL8deBMg7nSIteTQWUzOT2ieuTl2WC8Mw198v3DYpXH1BTz0s9A9lM/i2h1qRVzVW+TMjrg==", "type": "package", "path": "volo.abp.features/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "lib/netstandard2.0/Volo.Abp.Features.xml", "volo.abp.features.4.3.0.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.GlobalFeatures/4.3.0": {"sha512": "DvDZ+7aQzEB2gtcRBJVFYiobfU5Rzj3p6Wsbp8XdEOlYLQXP55Ga8wiUMtMH4crOzHYSwHbdPC1s2bHMgVM9rA==", "type": "package", "path": "volo.abp.globalfeatures/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.xml", "volo.abp.globalfeatures.4.3.0.nupkg.sha512", "volo.abp.globalfeatures.nuspec"]}, "Volo.Abp.Guids/4.3.0": {"sha512": "Czq7YNU8zrSLQMx+8tpRbxjqN0eOmXAcr7XXy/qJSIoFLGCgh9xkH5CQhKmEFzl0vJFXf4DiHHEPAoRbW6mrhw==", "type": "package", "path": "volo.abp.guids/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "lib/netstandard2.0/Volo.Abp.Guids.xml", "volo.abp.guids.4.3.0.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Http/4.3.0": {"sha512": "FXszHkbaNSbq12frlgGo/7IcolTjbb65KASaDYeLXlaJBkM5PRpzfgufHChgYlTT/QfX6V0B+xVXcSn4hf9H1w==", "type": "package", "path": "volo.abp.http/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Http.dll", "lib/netstandard2.0/Volo.Abp.Http.pdb", "lib/netstandard2.0/Volo.Abp.Http.xml", "volo.abp.http.4.3.0.nupkg.sha512", "volo.abp.http.nuspec"]}, "Volo.Abp.Http.Abstractions/4.3.0": {"sha512": "Fy32oLk5amHZlObVzCz+FWDcjqd06IOhDSN10dg6Cueaefh/BRTd72tGSHPrEpKpjLLvnCU/L19ZIZd9DJAabQ==", "type": "package", "path": "volo.abp.http.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.xml", "volo.abp.http.abstractions.4.3.0.nupkg.sha512", "volo.abp.http.abstractions.nuspec"]}, "Volo.Abp.Json/4.3.0": {"sha512": "yZJ/JjJeEO7vaQ/5vH1k0WUDPLjr7rkJ/JkhB6yjRDVirISPWL3y2CbTh6NPSyjTBShkaETCsOeAQBbjDvEbPQ==", "type": "package", "path": "volo.abp.json/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "lib/netstandard2.0/Volo.Abp.Json.xml", "volo.abp.json.4.3.0.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Localization/4.3.0": {"sha512": "d5yfg69jxE+rvyhjR1RoeF5KW0gNay2xnhixa6gAOXQ/8ZG7WmgU+G5Aa8GjgFzccL9XgFJYq7Ehzwtz3BS5ZA==", "type": "package", "path": "volo.abp.localization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "lib/netstandard2.0/Volo.Abp.Localization.xml", "volo.abp.localization.4.3.0.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/4.3.0": {"sha512": "8edOpld98PimnPODkrWtw/chW+K+1oAQUZccaO6F1DdnUsknQ03k6tXNvdVaXDQft74SsMcaf1tlHrvfrKPhZA==", "type": "package", "path": "volo.abp.localization.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.xml", "volo.abp.localization.abstractions.4.3.0.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.Minify/4.3.0": {"sha512": "i7XOHJmigo0XsG+Il0AHuwrGnFrdkkgFmAcjeziwMEo52W2FkGZG7DMDQ9n/TD+cBYLQCQ7ckDxxlBvZU51jeg==", "type": "package", "path": "volo.abp.minify/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Minify.dll", "lib/netstandard2.0/Volo.Abp.Minify.pdb", "lib/netstandard2.0/Volo.Abp.Minify.xml", "volo.abp.minify.4.3.0.nupkg.sha512", "volo.abp.minify.nuspec"]}, "Volo.Abp.MultiTenancy/4.3.0": {"sha512": "/DU+5xg+7v889I86EguH+s/2C8wleVaphlTAuRgSPlWIujeOh6O7Dj+ImBb+LOEFK+6PvnzDyDCca57k2lwRsw==", "type": "package", "path": "volo.abp.multitenancy/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.xml", "volo.abp.multitenancy.4.3.0.nupkg.sha512", "volo.abp.multitenancy.nuspec"]}, "Volo.Abp.ObjectExtending/4.3.0": {"sha512": "CBfao2YPtulP+ZaDrNe0Tx/K9bAiodv+gqYVWQ7UsrVloLIiQ1EMFv0Ln5zI9O111cgQHfLvEVYiFguP2GjSjA==", "type": "package", "path": "volo.abp.objectextending/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.0/Volo.Abp.ObjectExtending.xml", "volo.abp.objectextending.4.3.0.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.ObjectMapping/4.3.0": {"sha512": "4QwdHZFbDiLMW4QNaQvDqjmHw5a3BEKhDmGUkWYwqNHVBTpwj/t7Fm+N0g1FXGjYE5muioko168pbsA8oPwDDw==", "type": "package", "path": "volo.abp.objectmapping/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.0/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.0/Volo.Abp.ObjectMapping.xml", "volo.abp.objectmapping.4.3.0.nupkg.sha512", "volo.abp.objectmapping.nuspec"]}, "Volo.Abp.Security/4.3.0": {"sha512": "HNShcU4urZUP4McuJFqv/X3m2tyJQkrruvuIbV+4MR9a817zo82BPqmzYG7RrZnG2Spyvqaa8GtXBfCGgFVUZA==", "type": "package", "path": "volo.abp.security/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "lib/netstandard2.0/Volo.Abp.Security.xml", "volo.abp.security.4.3.0.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.SettingManagement.Application.Contracts/4.3.0": {"sha512": "i9dCc5OGdynForx95Ypjmb37oPPLl+yrjmTqNwmy0xuCGBqmkMDnL7oZ111x89OfP8/ZTKx2LAsywT5qKa1Ijw==", "type": "package", "path": "volo.abp.settingmanagement.application.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.SettingManagement.Application.Contracts.xml", "volo.abp.settingmanagement.application.contracts.4.3.0.nupkg.sha512", "volo.abp.settingmanagement.application.contracts.nuspec"]}, "Volo.Abp.SettingManagement.Domain.Shared/4.3.0": {"sha512": "Wtv5TitvfXhR8+lOIUz1p4gAkcUxVZP8qeKW5SjaNqDFvZnJNZTj8ug+u9Hi8YfKxPpZ74n9gSZ7VUYWFwrDMQ==", "type": "package", "path": "volo.abp.settingmanagement.domain.shared/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.SettingManagement.Domain.Shared.xml", "volo.abp.settingmanagement.domain.shared.4.3.0.nupkg.sha512", "volo.abp.settingmanagement.domain.shared.nuspec"]}, "Volo.Abp.SettingManagement.HttpApi/4.3.0": {"sha512": "I/Jg/2PjC2qhCdoqC2L7omPaRw1n8yxFWOtm007HoVnf5XOEYrda/JQ6sjRSHYZDdYAW2azsgXWcNxGzBhv42A==", "type": "package", "path": "volo.abp.settingmanagement.httpapi/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.SettingManagement.HttpApi.dll", "lib/net5.0/Volo.Abp.SettingManagement.HttpApi.pdb", "lib/net5.0/Volo.Abp.SettingManagement.HttpApi.xml", "volo.abp.settingmanagement.httpapi.4.3.0.nupkg.sha512", "volo.abp.settingmanagement.httpapi.nuspec"]}, "Volo.Abp.SettingManagement.Web/4.3.0": {"sha512": "upx6yOOkmUYLoAKe8lIPkcspTDbcOdQxUklUQWXrzDPvZ276GfVF4X84r/CgVzK31xyqH+kw7DVv8V89QRKyjw==", "type": "package", "path": "volo.abp.settingmanagement.web/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Volo.Abp.SettingManagement.Web.Views.dll", "lib/net5.0/Volo.Abp.SettingManagement.Web.Views.pdb", "lib/net5.0/Volo.Abp.SettingManagement.Web.dll", "lib/net5.0/Volo.Abp.SettingManagement.Web.pdb", "lib/net5.0/Volo.Abp.SettingManagement.Web.xml", "volo.abp.settingmanagement.web.4.3.0.nupkg.sha512", "volo.abp.settingmanagement.web.nuspec"]}, "Volo.Abp.Settings/4.3.0": {"sha512": "aCEhagDlTzoS+c/RI/geH7YlK+d3fYvQ/WRI75PLVJMMhMALXT25cZr71BsNwu00LLUnPf6xFfLkfwlsZfIaKA==", "type": "package", "path": "volo.abp.settings/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "lib/netstandard2.0/Volo.Abp.Settings.xml", "volo.abp.settings.4.3.0.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.Specifications/4.3.0": {"sha512": "c/Yg1LiFm+JPX5sGTS/OPxTRvaaEaNlkvCAJAwjTC2CkjrdjPSKDEiB47tCWHYW0dIcIyEx/bV9L5inyLc2qAQ==", "type": "package", "path": "volo.abp.specifications/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Specifications.dll", "lib/netstandard2.0/Volo.Abp.Specifications.pdb", "lib/netstandard2.0/Volo.Abp.Specifications.xml", "volo.abp.specifications.4.3.0.nupkg.sha512", "volo.abp.specifications.nuspec"]}, "Volo.Abp.TextTemplating/4.3.0": {"sha512": "H2CVzU5534ydCB91/2XFLyCVCNg0G6VWe3PhcDm1zIBhH+lQip/qRm06UY/hdRn0pC4EmY/BozD1LG6xYlvzFw==", "type": "package", "path": "volo.abp.texttemplating/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.TextTemplating.dll", "lib/netstandard2.0/Volo.Abp.TextTemplating.pdb", "lib/netstandard2.0/Volo.Abp.TextTemplating.xml", "volo.abp.texttemplating.4.3.0.nupkg.sha512", "volo.abp.texttemplating.nuspec"]}, "Volo.Abp.Threading/4.3.0": {"sha512": "HGZ7MNUNUamMKwhp9taCv8LTnsltA3WchiO0qIuf1BRAg3333muQqrtmraIB/k6T+1ecPoEIpxXzxwgMmTvvxg==", "type": "package", "path": "volo.abp.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "lib/netstandard2.0/Volo.Abp.Threading.xml", "volo.abp.threading.4.3.0.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/4.3.0": {"sha512": "ohLNz/1T1sKLFV0IXeqxrMlu2sf/qdM3Bj4hOzhse9MGB+jO2l8DKmzOOH70xpH6wWrRELjmRyOoR8U3jwc8Hg==", "type": "package", "path": "volo.abp.timing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "lib/netstandard2.0/Volo.Abp.Timing.xml", "volo.abp.timing.4.3.0.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.UI/4.3.0": {"sha512": "I2e6XYnu+q0ljJ7h+9YNGDUh8GI6gC3K+WvjrXMQ8MJi9iEUDfWJXp6XpXWqT8swwNqHRk/Dw4BOpAhTdJF9gw==", "type": "package", "path": "volo.abp.ui/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.UI.dll", "lib/netstandard2.0/Volo.Abp.UI.pdb", "lib/netstandard2.0/Volo.Abp.UI.xml", "volo.abp.ui.4.3.0.nupkg.sha512", "volo.abp.ui.nuspec"]}, "Volo.Abp.UI.Navigation/4.3.0": {"sha512": "JQW6TZUwXaTbsXxdgz4RUIXkISEmowIRU2tFbSaE7werawUlhaWl1NlKgx4QXFCA7qCiO0SUlJCkZqai99t32g==", "type": "package", "path": "volo.abp.ui.navigation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.UI.Navigation.dll", "lib/netstandard2.0/Volo.Abp.UI.Navigation.pdb", "lib/netstandard2.0/Volo.Abp.UI.Navigation.xml", "volo.abp.ui.navigation.4.3.0.nupkg.sha512", "volo.abp.ui.navigation.nuspec"]}, "Volo.Abp.Uow/4.3.0": {"sha512": "CwmJseLIBUcFsAiIVSr9WiNBcn/cqzJDCZmAYqzTLy7etETO+nMXSqohvAt6LlbwmjXHfMO5EZb6mYy4nnTbKQ==", "type": "package", "path": "volo.abp.uow/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "lib/netstandard2.0/Volo.Abp.Uow.xml", "volo.abp.uow.4.3.0.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Validation/4.3.0": {"sha512": "SIXT7AImi3XRgC1XIs1QJhyEXNOEugYgpR8dpB17sIBcOBdkz9r4XO6lxlSarT5+vo7I9Qut8zmH9S2T76nNWg==", "type": "package", "path": "volo.abp.validation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "lib/netstandard2.0/Volo.Abp.Validation.xml", "volo.abp.validation.4.3.0.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.Validation.Abstractions/4.3.0": {"sha512": "2dj1wA0YTVxA1wQQ0zE8R3a8nBk4fo/ibUYNV8aMRxkxX83e80BVNHi/5hMabG2ybHmTZrSLxquZyzwKvgSWkw==", "type": "package", "path": "volo.abp.validation.abstractions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.xml", "volo.abp.validation.abstractions.4.3.0.nupkg.sha512", "volo.abp.validation.abstractions.nuspec"]}, "Volo.Abp.VirtualFileSystem/4.3.0": {"sha512": "YL4rsAsqFzDOIOAGmCk78KskDcwLR8EsDxRdtgnztxGIEfok7c3/39nwbpwrkFBrwN5ZP0Qoo03VjFc+49cgqg==", "type": "package", "path": "volo.abp.virtualfilesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.xml", "volo.abp.virtualfilesystem.4.3.0.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "Volo.Abp.LeptonTheme.Management.Application.Contracts/1.0.0": {"type": "project", "path": "../Volo.Abp.LeptonTheme.Management.Application.Contracts/Volo.Abp.LeptonTheme.Management.Application.Contracts.csproj", "msbuildProject": "../Volo.Abp.LeptonTheme.Management.Application.Contracts/Volo.Abp.LeptonTheme.Management.Application.Contracts.csproj"}, "Volo.Abp.LeptonTheme.Management.Domain.Shared/1.0.0": {"type": "project", "path": "../Volo.Abp.LeptonTheme.Management.Domain.Shared/Volo.Abp.LeptonTheme.Management.Domain.Shared.csproj", "msbuildProject": "../Volo.Abp.LeptonTheme.Management.Domain.Shared/Volo.Abp.LeptonTheme.Management.Domain.Shared.csproj"}, "Volo.Abp.LeptonTheme.Management.HttpApi/1.0.0": {"type": "project", "path": "../Volo.Abp.LeptonTheme.Management.HttpApi/Volo.Abp.LeptonTheme.Management.HttpApi.csproj", "msbuildProject": "../Volo.Abp.LeptonTheme.Management.HttpApi/Volo.Abp.LeptonTheme.Management.HttpApi.csproj"}}, "projectFileDependencyGroups": {"net5.0": ["Microsoft.Extensions.FileProviders.Embedded >= 5.0.*", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared >= 4.3.0", "Volo.Abp.Authorization >= 4.3.0", "Volo.Abp.AutoMapper >= 4.3.0", "Volo.Abp.Commercial.Core >= 4.3.0", "Volo.Abp.LeptonTheme.Management.HttpApi >= 1.0.0", "Volo.Abp.SettingManagement.Web >= 4.3.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\src\\Volo.Abp.LeptonTheme.Management.Web\\Volo.Abp.LeptonTheme.Management.Web.csproj", "projectName": "Volo.Abp.LeptonTheme.Management.Web", "projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\src\\Volo.Abp.LeptonTheme.Management.Web\\Volo.Abp.LeptonTheme.Management.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\src\\Volo.Abp.LeptonTheme.Management.Web\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\src\\Volo.Abp.LeptonTheme.Management.HttpApi\\Volo.Abp.LeptonTheme.Management.HttpApi.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Source_Code\\AOMS_GITLAB\\aspnet-core\\modules\\Volo.LeptonTheme\\src\\Volo.Abp.LeptonTheme.Management.HttpApi\\Volo.Abp.LeptonTheme.Management.HttpApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[5.0.*, )"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Authorization": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.AutoMapper": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.Commercial.Core": {"target": "Package", "version": "[4.3.0, )"}, "Volo.Abp.SettingManagement.Web": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["portable-net45+win8+wp8+wpa81", "net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.408\\RuntimeIdentifierGraph.json"}}}}